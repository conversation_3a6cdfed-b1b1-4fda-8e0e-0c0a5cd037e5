<?php
/**
 * Security Scanner Installation Script
 * This script sets up the security scanner application
 */

class SecurityScannerInstaller {
    private $requiredDirectories = [
        'logs',
        'data',
        'reports',
        'quarantine',
        'config'
    ];
    
    private $requiredFiles = [
        'config/security_config.json'
    ];
    
    private $permissions = [
        'logs' => 0755,
        'data' => 0755,
        'reports' => 0755,
        'quarantine' => 0755,
        'config' => 0755
    ];
    
    public function install() {
        echo "<h1>Security Scanner Installation</h1>\n";
        
        // Check PHP version
        if (!$this->checkPhpVersion()) {
            return false;
        }
        
        // Check required extensions
        if (!$this->checkRequiredExtensions()) {
            return false;
        }
        
        // Create directories
        if (!$this->createDirectories()) {
            return false;
        }
        
        // Set permissions
        if (!$this->setPermissions()) {
            return false;
        }
        
        // Create configuration files
        if (!$this->createConfigFiles()) {
            return false;
        }
        
        // Initialize database files
        if (!$this->initializeDataFiles()) {
            return false;
        }
        
        // Create .htaccess files for security
        if (!$this->createSecurityFiles()) {
            return false;
        }
        
        echo "<div style='color: green; font-weight: bold;'>✓ Installation completed successfully!</div>\n";
        echo "<p>You can now access the security scanner at: <a href='index.php'>index.php</a></p>\n";
        
        return true;
    }
    
    private function checkPhpVersion() {
        $minVersion = '7.4.0';
        $currentVersion = PHP_VERSION;
        
        echo "<h3>Checking PHP Version</h3>\n";
        echo "Current PHP Version: $currentVersion<br>\n";
        echo "Required PHP Version: $minVersion<br>\n";
        
        if (version_compare($currentVersion, $minVersion, '<')) {
            echo "<div style='color: red;'>✗ PHP version $minVersion or higher is required</div>\n";
            return false;
        }
        
        echo "<div style='color: green;'>✓ PHP version check passed</div>\n";
        return true;
    }
    
    private function checkRequiredExtensions() {
        $requiredExtensions = [
            'json' => 'JSON support',
            'curl' => 'cURL support for webhooks',
            'mbstring' => 'Multibyte string support',
            'fileinfo' => 'File information support'
        ];
        
        echo "<h3>Checking Required Extensions</h3>\n";
        
        $allExtensionsLoaded = true;
        foreach ($requiredExtensions as $extension => $description) {
            if (extension_loaded($extension)) {
                echo "<div style='color: green;'>✓ $extension - $description</div>\n";
            } else {
                echo "<div style='color: red;'>✗ $extension - $description (MISSING)</div>\n";
                $allExtensionsLoaded = false;
            }
        }
        
        // Optional extensions
        $optionalExtensions = [
            'gd' => 'GD library for image processing',
            'zip' => 'ZIP support for log compression',
            'openssl' => 'OpenSSL for secure communications'
        ];
        
        echo "<h4>Optional Extensions</h4>\n";
        foreach ($optionalExtensions as $extension => $description) {
            if (extension_loaded($extension)) {
                echo "<div style='color: green;'>✓ $extension - $description</div>\n";
            } else {
                echo "<div style='color: orange;'>⚠ $extension - $description (recommended)</div>\n";
            }
        }
        
        return $allExtensionsLoaded;
    }
    
    private function createDirectories() {
        echo "<h3>Creating Directories</h3>\n";
        
        foreach ($this->requiredDirectories as $dir) {
            if (!is_dir($dir)) {
                if (mkdir($dir, 0755, true)) {
                    echo "<div style='color: green;'>✓ Created directory: $dir</div>\n";
                } else {
                    echo "<div style='color: red;'>✗ Failed to create directory: $dir</div>\n";
                    return false;
                }
            } else {
                echo "<div style='color: blue;'>ℹ Directory already exists: $dir</div>\n";
            }
        }
        
        return true;
    }
    
    private function setPermissions() {
        echo "<h3>Setting Permissions</h3>\n";
        
        foreach ($this->permissions as $path => $permission) {
            if (is_dir($path)) {
                if (chmod($path, $permission)) {
                    echo "<div style='color: green;'>✓ Set permissions for: $path (" . decoct($permission) . ")</div>\n";
                } else {
                    echo "<div style='color: orange;'>⚠ Could not set permissions for: $path</div>\n";
                }
            }
        }
        
        return true;
    }
    
    private function createConfigFiles() {
        echo "<h3>Creating Configuration Files</h3>\n";
        
        // Check if config file exists
        if (file_exists('config/security_config.json')) {
            echo "<div style='color: blue;'>ℹ Configuration file already exists</div>\n";
            return true;
        }
        
        // Create default config if it doesn't exist
        $defaultConfig = [
            "scanner" => [
                "scan_directories" => ["./"],
                "exclude_directories" => ["vendor/", "node_modules/", ".git/", "logs/", "data/", "tmp/", "cache/", "quarantine/"],
                "max_file_size" => 52428800,
                "suspicious_extensions" => [".exe", ".bat", ".cmd", ".scr", ".pif", ".com", ".vbs", ".js", ".jar"],
                "log_file" => "logs/security_scan.log",
                "alert_email" => "",
                "quarantine_dir" => "quarantine/"
            ],
            "monitor" => [
                "monitor_directories" => ["./"],
                "exclude_directories" => ["logs/", "data/", "tmp/", "cache/", ".git/", "quarantine/"],
                "exclude_extensions" => [".log", ".tmp", ".cache", ".bak"],
                "check_interval" => 300,
                "alert_on_new_files" => true,
                "alert_on_modified_files" => true,
                "alert_on_deleted_files" => true,
                "alert_on_permission_changes" => true,
                "max_file_size_monitor" => 10485760
            ]
        ];
        
        if (file_put_contents('config/security_config.json', json_encode($defaultConfig, JSON_PRETTY_PRINT))) {
            echo "<div style='color: green;'>✓ Created configuration file</div>\n";
            return true;
        } else {
            echo "<div style='color: red;'>✗ Failed to create configuration file</div>\n";
            return false;
        }
    }
    
    private function initializeDataFiles() {
        echo "<h3>Initializing Data Files</h3>\n";
        
        // Create empty baseline file
        $baselineFile = 'data/file_baseline.json';
        if (!file_exists($baselineFile)) {
            if (file_put_contents($baselineFile, '{}')) {
                echo "<div style='color: green;'>✓ Created baseline file</div>\n";
            } else {
                echo "<div style='color: red;'>✗ Failed to create baseline file</div>\n";
                return false;
            }
        }
        
        // Create threat signatures file
        $signaturesFile = 'data/threat_signatures.json';
        if (!file_exists($signaturesFile)) {
            $defaultSignatures = [
                "last_updated" => date('Y-m-d H:i:s'),
                "version" => "1.0",
                "signatures" => []
            ];
            
            if (file_put_contents($signaturesFile, json_encode($defaultSignatures, JSON_PRETTY_PRINT))) {
                echo "<div style='color: green;'>✓ Created threat signatures file</div>\n";
            } else {
                echo "<div style='color: red;'>✗ Failed to create threat signatures file</div>\n";
                return false;
            }
        }
        
        return true;
    }
    
    private function createSecurityFiles() {
        echo "<h3>Creating Security Files</h3>\n";
        
        // Create .htaccess for logs directory
        $logsHtaccess = "logs/.htaccess";
        $htaccessContent = "Order Deny,Allow\nDeny from all\n";
        
        if (file_put_contents($logsHtaccess, $htaccessContent)) {
            echo "<div style='color: green;'>✓ Created .htaccess for logs directory</div>\n";
        } else {
            echo "<div style='color: orange;'>⚠ Could not create .htaccess for logs directory</div>\n";
        }
        
        // Create .htaccess for data directory
        $dataHtaccess = "data/.htaccess";
        if (file_put_contents($dataHtaccess, $htaccessContent)) {
            echo "<div style='color: green;'>✓ Created .htaccess for data directory</div>\n";
        } else {
            echo "<div style='color: orange;'>⚠ Could not create .htaccess for data directory</div>\n";
        }
        
        // Create .htaccess for quarantine directory
        $quarantineHtaccess = "quarantine/.htaccess";
        if (file_put_contents($quarantineHtaccess, $htaccessContent)) {
            echo "<div style='color: green;'>✓ Created .htaccess for quarantine directory</div>\n";
        } else {
            echo "<div style='color: orange;'>⚠ Could not create .htaccess for quarantine directory</div>\n";
        }
        
        return true;
    }
    
    public function checkSystemRequirements() {
        echo "<h2>System Requirements Check</h2>\n";
        
        $requirements = [
            'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
            'Write permissions' => is_writable('.'),
            'JSON extension' => extension_loaded('json'),
            'cURL extension' => extension_loaded('curl'),
            'File info extension' => extension_loaded('fileinfo')
        ];
        
        $allMet = true;
        foreach ($requirements as $requirement => $met) {
            $status = $met ? '✓' : '✗';
            $color = $met ? 'green' : 'red';
            echo "<div style='color: $color;'>$status $requirement</div>\n";
            
            if (!$met) {
                $allMet = false;
            }
        }
        
        return $allMet;
    }
}

// Run installation if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'install.php') {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Security Scanner Installation</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
            .button:hover { background: #005a87; }
        </style>
    </head>
    <body>
        <div class="container">
            <?php
            $installer = new SecurityScannerInstaller();
            
            if (isset($_GET['action']) && $_GET['action'] === 'install') {
                $installer->install();
                echo "<br><a href='index.php' class='button'>Go to Security Scanner</a>";
            } else {
                echo "<h1>Security Scanner Installation</h1>";
                echo "<p>This will install and configure the Security Scanner application.</p>";
                
                if ($installer->checkSystemRequirements()) {
                    echo "<br><a href='?action=install' class='button'>Start Installation</a>";
                } else {
                    echo "<br><div style='color: red; font-weight: bold;'>Please fix the system requirements before proceeding.</div>";
                }
            }
            ?>
        </div>
    </body>
    </html>
    <?php
}
?>
