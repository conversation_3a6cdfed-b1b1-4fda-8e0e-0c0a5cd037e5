<?php

class MobileSecurityChecker {
    private $config = [];
    private $deviceInfo = [];
    private $securityChecks = [];
    
    public function __construct($config = []) {
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->initializeSecurityChecks();
    }
    
    private function getDefaultConfig() {
        return [
            'enable_app_analysis' => true,
            'enable_permission_check' => true,
            'enable_device_integrity' => true,
            'enable_network_security' => true,
            'enable_malware_detection' => true,
            'suspicious_apps' => [
                'com.malware.example',
                'suspicious.package.name'
            ],
            'dangerous_permissions' => [
                'android.permission.SYSTEM_ALERT_WINDOW',
                'android.permission.DEVICE_ADMIN',
                'android.permission.ACCESSIBILITY_SERVICE'
            ]
        ];
    }
    
    private function initializeSecurityChecks() {
        $this->securityChecks = [
            'device_rooted' => [$this, 'checkDeviceRooted'],
            'debug_enabled' => [$this, 'checkDebugEnabled'],
            'unknown_sources' => [$this, 'checkUnknownSources'],
            'suspicious_apps' => [$this, 'checkSuspiciousApps'],
            'dangerous_permissions' => [$this, 'checkDangerousPermissions'],
            'network_security' => [$this, 'checkNetworkSecurity'],
            'device_encryption' => [$this, 'checkDeviceEncryption'],
            'screen_lock' => [$this, 'checkScreenLock'],
            'app_integrity' => [$this, 'checkAppIntegrity'],
            'malware_signatures' => [$this, 'checkMalwareSignatures']
        ];
    }
    
    public function performSecurityAnalysis($deviceData = []) {
        $this->deviceInfo = $deviceData;
        $results = [];
        $overallRisk = 0;
        
        foreach ($this->securityChecks as $checkName => $checkFunction) {
            if ($this->isCheckEnabled($checkName)) {
                $result = call_user_func($checkFunction);
                $results[$checkName] = $result;
                $overallRisk += $result['risk_score'];
            }
        }
        
        return [
            'device_id' => $this->deviceInfo['device_id'] ?? 'unknown',
            'platform' => $this->deviceInfo['platform'] ?? 'unknown',
            'analysis_timestamp' => date('c'),
            'overall_risk_score' => min($overallRisk, 100),
            'risk_level' => $this->calculateRiskLevel($overallRisk),
            'security_checks' => $results,
            'recommendations' => $this->generateRecommendations($results)
        ];
    }
    
    private function checkDeviceRooted() {
        $indicators = [
            'su_binary' => $this->checkSuBinary(),
            'root_apps' => $this->checkRootApps(),
            'build_tags' => $this->checkBuildTags(),
            'system_properties' => $this->checkSystemProperties()
        ];
        
        $riskScore = 0;
        $isRooted = false;
        
        foreach ($indicators as $indicator => $detected) {
            if ($detected) {
                $isRooted = true;
                $riskScore += 20;
            }
        }
        
        return [
            'status' => $isRooted ? 'ROOTED' : 'NOT_ROOTED',
            'risk_score' => min($riskScore, 80),
            'indicators' => $indicators,
            'description' => $isRooted ? 'Device appears to be rooted/jailbroken' : 'Device integrity intact'
        ];
    }
    
    private function checkSuBinary() {
        $suPaths = [
            '/system/app/Superuser.apk',
            '/sbin/su',
            '/system/bin/su',
            '/system/xbin/su',
            '/data/local/xbin/su',
            '/data/local/bin/su',
            '/system/sd/xbin/su',
            '/system/bin/failsafe/su',
            '/data/local/su'
        ];
        
        foreach ($suPaths as $path) {
            if (file_exists($path)) {
                return true;
            }
        }
        
        return false;
    }
    
    private function checkRootApps() {
        $rootApps = [
            'com.noshufou.android.su',
            'com.thirdparty.superuser',
            'eu.chainfire.supersu',
            'com.koushikdutta.superuser',
            'com.zachspong.temprootremovejb',
            'com.ramdroid.appquarantine'
        ];
        
        $installedApps = $this->deviceInfo['installed_apps'] ?? [];
        
        foreach ($rootApps as $rootApp) {
            if (in_array($rootApp, $installedApps)) {
                return true;
            }
        }
        
        return false;
    }
    
    private function checkBuildTags() {
        $buildTags = $this->deviceInfo['build_tags'] ?? '';
        return strpos($buildTags, 'test-keys') !== false;
    }
    
    private function checkSystemProperties() {
        $properties = $this->deviceInfo['system_properties'] ?? [];
        
        $suspiciousProps = [
            'ro.debuggable' => '1',
            'ro.secure' => '0',
            'service.adb.root' => '1'
        ];
        
        foreach ($suspiciousProps as $prop => $value) {
            if (isset($properties[$prop]) && $properties[$prop] === $value) {
                return true;
            }
        }
        
        return false;
    }
    
    private function checkDebugEnabled() {
        $debugEnabled = $this->deviceInfo['debug_enabled'] ?? false;
        
        return [
            'status' => $debugEnabled ? 'ENABLED' : 'DISABLED',
            'risk_score' => $debugEnabled ? 30 : 0,
            'description' => $debugEnabled ? 'USB debugging is enabled' : 'USB debugging is disabled'
        ];
    }
    
    private function checkUnknownSources() {
        $unknownSources = $this->deviceInfo['unknown_sources_enabled'] ?? false;
        
        return [
            'status' => $unknownSources ? 'ENABLED' : 'DISABLED',
            'risk_score' => $unknownSources ? 25 : 0,
            'description' => $unknownSources ? 'Installation from unknown sources is enabled' : 'Unknown sources disabled'
        ];
    }
    
    private function checkSuspiciousApps() {
        $installedApps = $this->deviceInfo['installed_apps'] ?? [];
        $suspiciousFound = [];
        
        foreach ($this->config['suspicious_apps'] as $suspiciousApp) {
            if (in_array($suspiciousApp, $installedApps)) {
                $suspiciousFound[] = $suspiciousApp;
            }
        }
        
        // Check for apps with suspicious characteristics
        $appDetails = $this->deviceInfo['app_details'] ?? [];
        foreach ($appDetails as $app) {
            if ($this->isAppSuspicious($app)) {
                $suspiciousFound[] = $app['package_name'];
            }
        }
        
        return [
            'status' => empty($suspiciousFound) ? 'CLEAN' : 'SUSPICIOUS_APPS_FOUND',
            'risk_score' => count($suspiciousFound) * 15,
            'suspicious_apps' => $suspiciousFound,
            'description' => empty($suspiciousFound) ? 'No suspicious apps detected' : 'Suspicious apps found: ' . implode(', ', $suspiciousFound)
        ];
    }
    
    private function isAppSuspicious($app) {
        $suspiciousIndicators = [
            'unsigned' => !($app['is_signed'] ?? true),
            'system_permissions' => $this->hasSystemPermissions($app['permissions'] ?? []),
            'obfuscated' => $this->isAppObfuscated($app),
            'suspicious_name' => $this->hasSuspiciousName($app['name'] ?? ''),
            'unknown_developer' => $this->isUnknownDeveloper($app['developer'] ?? '')
        ];
        
        $suspiciousCount = array_sum($suspiciousIndicators);
        return $suspiciousCount >= 2; // Threshold for suspicious app
    }
    
    private function hasSystemPermissions($permissions) {
        $systemPermissions = [
            'android.permission.SYSTEM_ALERT_WINDOW',
            'android.permission.DEVICE_ADMIN',
            'android.permission.ACCESSIBILITY_SERVICE',
            'android.permission.BIND_DEVICE_ADMIN'
        ];
        
        return !empty(array_intersect($permissions, $systemPermissions));
    }
    
    private function isAppObfuscated($app) {
        // Check for code obfuscation indicators
        $indicators = [
            'encrypted_strings' => $app['has_encrypted_strings'] ?? false,
            'packed_code' => $app['is_packed'] ?? false,
            'anti_debug' => $app['has_anti_debug'] ?? false
        ];
        
        return array_sum($indicators) >= 2;
    }
    
    private function hasSuspiciousName($name) {
        $suspiciousKeywords = ['hack', 'crack', 'mod', 'cheat', 'exploit', 'bypass'];
        
        foreach ($suspiciousKeywords as $keyword) {
            if (stripos($name, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    private function isUnknownDeveloper($developer) {
        $trustedDevelopers = ['Google LLC', 'Apple Inc.', 'Microsoft Corporation'];
        return !in_array($developer, $trustedDevelopers) && empty($developer);
    }
    
    private function checkDangerousPermissions() {
        $appPermissions = $this->deviceInfo['app_permissions'] ?? [];
        $dangerousApps = [];
        
        foreach ($appPermissions as $app => $permissions) {
            $dangerousPerms = array_intersect($permissions, $this->config['dangerous_permissions']);
            if (!empty($dangerousPerms)) {
                $dangerousApps[$app] = $dangerousPerms;
            }
        }
        
        return [
            'status' => empty($dangerousApps) ? 'SAFE' : 'DANGEROUS_PERMISSIONS_FOUND',
            'risk_score' => count($dangerousApps) * 10,
            'dangerous_apps' => $dangerousApps,
            'description' => empty($dangerousApps) ? 'No dangerous permissions detected' : 'Apps with dangerous permissions found'
        ];
    }
    
    private function checkNetworkSecurity() {
        $networkInfo = $this->deviceInfo['network_info'] ?? [];
        $riskScore = 0;
        $issues = [];
        
        // Check for insecure connections
        if ($networkInfo['uses_http'] ?? false) {
            $riskScore += 15;
            $issues[] = 'Uses insecure HTTP connections';
        }
        
        // Check for open WiFi
        if ($networkInfo['connected_to_open_wifi'] ?? false) {
            $riskScore += 20;
            $issues[] = 'Connected to open WiFi network';
        }
        
        // Check for VPN usage
        if (!($networkInfo['vpn_enabled'] ?? false)) {
            $riskScore += 10;
            $issues[] = 'VPN not enabled';
        }
        
        return [
            'status' => empty($issues) ? 'SECURE' : 'NETWORK_ISSUES_FOUND',
            'risk_score' => $riskScore,
            'issues' => $issues,
            'description' => empty($issues) ? 'Network security is good' : 'Network security issues detected'
        ];
    }
    
    private function checkDeviceEncryption() {
        $encrypted = $this->deviceInfo['device_encrypted'] ?? false;
        
        return [
            'status' => $encrypted ? 'ENCRYPTED' : 'NOT_ENCRYPTED',
            'risk_score' => $encrypted ? 0 : 25,
            'description' => $encrypted ? 'Device storage is encrypted' : 'Device storage is not encrypted'
        ];
    }
    
    private function checkScreenLock() {
        $screenLock = $this->deviceInfo['screen_lock_enabled'] ?? false;
        $lockType = $this->deviceInfo['screen_lock_type'] ?? 'none';
        
        $riskScore = 0;
        if (!$screenLock) {
            $riskScore = 30;
        } elseif (in_array($lockType, ['pattern', 'pin'])) {
            $riskScore = 10; // Weak lock types
        }
        
        return [
            'status' => $screenLock ? 'ENABLED' : 'DISABLED',
            'lock_type' => $lockType,
            'risk_score' => $riskScore,
            'description' => $screenLock ? "Screen lock enabled ($lockType)" : 'No screen lock configured'
        ];
    }
    
    private function checkAppIntegrity() {
        $appIntegrity = $this->deviceInfo['app_integrity'] ?? [];
        $modifiedApps = [];
        
        foreach ($appIntegrity as $app => $integrity) {
            if (!$integrity['signature_valid'] || $integrity['modified']) {
                $modifiedApps[] = $app;
            }
        }
        
        return [
            'status' => empty($modifiedApps) ? 'INTACT' : 'MODIFIED_APPS_FOUND',
            'risk_score' => count($modifiedApps) * 20,
            'modified_apps' => $modifiedApps,
            'description' => empty($modifiedApps) ? 'All apps have valid signatures' : 'Modified or unsigned apps detected'
        ];
    }
    
    private function checkMalwareSignatures() {
        $scanResults = $this->deviceInfo['malware_scan_results'] ?? [];
        $threatsFound = $scanResults['threats_found'] ?? 0;
        
        return [
            'status' => $threatsFound > 0 ? 'MALWARE_DETECTED' : 'CLEAN',
            'risk_score' => $threatsFound * 25,
            'threats_count' => $threatsFound,
            'threat_details' => $scanResults['threat_details'] ?? [],
            'description' => $threatsFound > 0 ? "Malware detected: $threatsFound threats" : 'No malware signatures detected'
        ];
    }
    
    private function isCheckEnabled($checkName) {
        $enabledChecks = [
            'device_rooted' => $this->config['enable_device_integrity'],
            'debug_enabled' => $this->config['enable_device_integrity'],
            'unknown_sources' => $this->config['enable_device_integrity'],
            'suspicious_apps' => $this->config['enable_app_analysis'],
            'dangerous_permissions' => $this->config['enable_permission_check'],
            'network_security' => $this->config['enable_network_security'],
            'device_encryption' => $this->config['enable_device_integrity'],
            'screen_lock' => $this->config['enable_device_integrity'],
            'app_integrity' => $this->config['enable_app_analysis'],
            'malware_signatures' => $this->config['enable_malware_detection']
        ];
        
        return $enabledChecks[$checkName] ?? true;
    }
    
    private function calculateRiskLevel($riskScore) {
        if ($riskScore >= 80) return 'critical';
        if ($riskScore >= 60) return 'high';
        if ($riskScore >= 40) return 'medium';
        if ($riskScore >= 20) return 'low';
        return 'minimal';
    }
    
    private function generateRecommendations($results) {
        $recommendations = [];
        
        foreach ($results as $checkName => $result) {
            if ($result['risk_score'] > 0) {
                $recommendations[] = $this->getRecommendationForCheck($checkName, $result);
            }
        }
        
        return $recommendations;
    }
    
    private function getRecommendationForCheck($checkName, $result) {
        $recommendations = [
            'device_rooted' => 'Consider using a non-rooted device for better security',
            'debug_enabled' => 'Disable USB debugging when not needed',
            'unknown_sources' => 'Disable installation from unknown sources',
            'suspicious_apps' => 'Remove suspicious applications immediately',
            'dangerous_permissions' => 'Review and revoke unnecessary dangerous permissions',
            'network_security' => 'Use secure connections and enable VPN',
            'device_encryption' => 'Enable device encryption in security settings',
            'screen_lock' => 'Set up a strong screen lock (biometric or strong password)',
            'app_integrity' => 'Reinstall modified apps from official sources',
            'malware_signatures' => 'Remove detected malware immediately'
        ];
        
        return [
            'check' => $checkName,
            'priority' => $result['risk_score'] >= 50 ? 'high' : 'medium',
            'recommendation' => $recommendations[$checkName] ?? 'Review security settings',
            'risk_score' => $result['risk_score']
        ];
    }
    
    public function generateMobileSecurityReport($analysisResults) {
        return [
            'report_id' => uniqid('mobile_'),
            'generated_at' => date('c'),
            'device_info' => [
                'device_id' => $this->deviceInfo['device_id'] ?? 'unknown',
                'platform' => $this->deviceInfo['platform'] ?? 'unknown',
                'os_version' => $this->deviceInfo['os_version'] ?? 'unknown',
                'device_model' => $this->deviceInfo['device_model'] ?? 'unknown'
            ],
            'security_analysis' => $analysisResults,
            'summary' => [
                'overall_risk' => $analysisResults['risk_level'],
                'risk_score' => $analysisResults['overall_risk_score'],
                'checks_performed' => count($analysisResults['security_checks']),
                'issues_found' => count(array_filter($analysisResults['security_checks'], function($check) {
                    return $check['risk_score'] > 0;
                }))
            ],
            'recommendations' => $analysisResults['recommendations']
        ];
    }
}

?>
