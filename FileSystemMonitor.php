<?php

class FileSystemMonitor {
    private $baselineFile = 'data/file_baseline.json';
    private $alertsFile = 'logs/file_alerts.log';
    private $config = [];
    private $baseline = [];
    private $currentState = [];
    
    public function __construct($config = []) {
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->loadBaseline();
    }
    
    private function getDefaultConfig() {
        return [
            'monitor_directories' => ['./'],
            'exclude_directories' => ['logs/', 'data/', 'tmp/', 'cache/', '.git/'],
            'exclude_extensions' => ['.log', '.tmp', '.cache'],
            'check_interval' => 300, // 5 minutes
            'alert_on_new_files' => true,
            'alert_on_modified_files' => true,
            'alert_on_deleted_files' => true,
            'alert_on_permission_changes' => true,
            'max_file_size_monitor' => 10 * 1024 * 1024 // 10MB
        ];
    }
    
    public function createBaseline() {
        $this->log("Creating file system baseline...");
        $this->baseline = [];
        
        foreach ($this->config['monitor_directories'] as $directory) {
            $this->scanDirectoryForBaseline($directory);
        }
        
        $this->saveBaseline();
        $this->log("Baseline created with " . count($this->baseline) . " files.");
        
        return count($this->baseline);
    }
    
    private function scanDirectoryForBaseline($directory) {
        if (!is_dir($directory)) {
            return;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filePath = $file->getPathname();
                
                // Skip excluded directories and files
                if ($this->shouldExcludeFile($filePath)) {
                    continue;
                }
                
                $this->baseline[$filePath] = $this->getFileInfo($filePath);
            }
        }
    }
    
    private function shouldExcludeFile($filePath) {
        // Check excluded directories
        foreach ($this->config['exclude_directories'] as $excludeDir) {
            if (strpos($filePath, $excludeDir) !== false) {
                return true;
            }
        }
        
        // Check excluded extensions
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        if (in_array('.' . $extension, $this->config['exclude_extensions'])) {
            return true;
        }
        
        // Check file size
        if (file_exists($filePath) && filesize($filePath) > $this->config['max_file_size_monitor']) {
            return true;
        }
        
        return false;
    }
    
    private function getFileInfo($filePath) {
        if (!file_exists($filePath)) {
            return null;
        }
        
        $stat = stat($filePath);
        
        return [
            'size' => $stat['size'],
            'mtime' => $stat['mtime'],
            'ctime' => $stat['ctime'],
            'permissions' => sprintf('%o', $stat['mode'] & 0777),
            'hash' => md5_file($filePath),
            'owner' => function_exists('posix_getpwuid') ? posix_getpwuid($stat['uid'])['name'] : $stat['uid'],
            'group' => function_exists('posix_getgrgid') ? posix_getgrgid($stat['gid'])['name'] : $stat['gid']
        ];
    }
    
    public function checkForChanges() {
        $this->log("Starting file system integrity check...");
        $changes = [];
        $this->currentState = [];
        
        // Scan current state
        foreach ($this->config['monitor_directories'] as $directory) {
            $this->scanDirectoryForChanges($directory);
        }
        
        // Check for new files
        if ($this->config['alert_on_new_files']) {
            $newFiles = array_diff_key($this->currentState, $this->baseline);
            foreach ($newFiles as $filePath => $fileInfo) {
                $changes[] = [
                    'type' => 'new_file',
                    'file' => $filePath,
                    'severity' => $this->assessNewFileSeverity($filePath),
                    'details' => $fileInfo,
                    'timestamp' => time()
                ];
            }
        }
        
        // Check for deleted files
        if ($this->config['alert_on_deleted_files']) {
            $deletedFiles = array_diff_key($this->baseline, $this->currentState);
            foreach ($deletedFiles as $filePath => $fileInfo) {
                $changes[] = [
                    'type' => 'deleted_file',
                    'file' => $filePath,
                    'severity' => 'medium',
                    'details' => $fileInfo,
                    'timestamp' => time()
                ];
            }
        }
        
        // Check for modified files
        if ($this->config['alert_on_modified_files']) {
            $commonFiles = array_intersect_key($this->baseline, $this->currentState);
            foreach ($commonFiles as $filePath => $baselineInfo) {
                $currentInfo = $this->currentState[$filePath];
                $modifications = $this->compareFileInfo($baselineInfo, $currentInfo);
                
                if (!empty($modifications)) {
                    $changes[] = [
                        'type' => 'modified_file',
                        'file' => $filePath,
                        'severity' => $this->assessModificationSeverity($modifications),
                        'modifications' => $modifications,
                        'baseline' => $baselineInfo,
                        'current' => $currentInfo,
                        'timestamp' => time()
                    ];
                }
            }
        }
        
        // Log and alert on changes
        if (!empty($changes)) {
            $this->processChanges($changes);
        }
        
        $this->log("File system check completed. Found " . count($changes) . " changes.");
        return $changes;
    }
    
    private function scanDirectoryForChanges($directory) {
        if (!is_dir($directory)) {
            return;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filePath = $file->getPathname();
                
                if ($this->shouldExcludeFile($filePath)) {
                    continue;
                }
                
                $this->currentState[$filePath] = $this->getFileInfo($filePath);
            }
        }
    }
    
    private function assessNewFileSeverity($filePath) {
        $fileName = basename($filePath);
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        // High risk file types
        $highRiskExtensions = ['php', 'asp', 'aspx', 'jsp', 'exe', 'bat', 'cmd', 'sh'];
        if (in_array($extension, $highRiskExtensions)) {
            return 'high';
        }
        
        // Suspicious file names
        $suspiciousNames = ['shell', 'backdoor', 'hack', 'exploit', 'payload'];
        foreach ($suspiciousNames as $suspicious) {
            if (stripos($fileName, $suspicious) !== false) {
                return 'high';
            }
        }
        
        // Hidden files
        if (strpos($fileName, '.') === 0) {
            return 'medium';
        }
        
        return 'low';
    }
    
    private function compareFileInfo($baseline, $current) {
        $modifications = [];
        
        if ($baseline['size'] !== $current['size']) {
            $modifications['size_changed'] = [
                'from' => $baseline['size'],
                'to' => $current['size']
            ];
        }
        
        if ($baseline['mtime'] !== $current['mtime']) {
            $modifications['modified_time_changed'] = [
                'from' => date('Y-m-d H:i:s', $baseline['mtime']),
                'to' => date('Y-m-d H:i:s', $current['mtime'])
            ];
        }
        
        if ($baseline['hash'] !== $current['hash']) {
            $modifications['content_changed'] = [
                'from' => $baseline['hash'],
                'to' => $current['hash']
            ];
        }
        
        if ($this->config['alert_on_permission_changes'] && $baseline['permissions'] !== $current['permissions']) {
            $modifications['permissions_changed'] = [
                'from' => $baseline['permissions'],
                'to' => $current['permissions']
            ];
        }
        
        if ($baseline['owner'] !== $current['owner']) {
            $modifications['owner_changed'] = [
                'from' => $baseline['owner'],
                'to' => $current['owner']
            ];
        }
        
        return $modifications;
    }
    
    private function assessModificationSeverity($modifications) {
        // Content changes are always high priority
        if (isset($modifications['content_changed'])) {
            return 'high';
        }
        
        // Permission changes to executable
        if (isset($modifications['permissions_changed'])) {
            $newPerms = $modifications['permissions_changed']['to'];
            if (strpos($newPerms, '7') !== false || strpos($newPerms, '5') !== false) {
                return 'high';
            }
            return 'medium';
        }
        
        // Owner changes
        if (isset($modifications['owner_changed'])) {
            return 'medium';
        }
        
        return 'low';
    }
    
    private function processChanges($changes) {
        foreach ($changes as $change) {
            $this->alertChange($change);
        }
    }
    
    private function alertChange($change) {
        $message = sprintf(
            "[%s] %s: %s (Severity: %s)",
            date('Y-m-d H:i:s', $change['timestamp']),
            strtoupper($change['type']),
            $change['file'],
            strtoupper($change['severity'])
        );
        
        if (isset($change['modifications'])) {
            $message .= " - Changes: " . implode(', ', array_keys($change['modifications']));
        }
        
        $this->log($message, $this->alertsFile);
        
        // Send email alert for high severity changes
        if ($change['severity'] === 'high' && !empty($this->config['alert_email'])) {
            $this->sendEmailAlert($change);
        }
    }
    
    private function sendEmailAlert($change) {
        $subject = "Security Alert: " . ucfirst($change['type']) . " detected";
        $body = "A security event has been detected:\n\n";
        $body .= "Type: " . $change['type'] . "\n";
        $body .= "File: " . $change['file'] . "\n";
        $body .= "Severity: " . $change['severity'] . "\n";
        $body .= "Time: " . date('Y-m-d H:i:s', $change['timestamp']) . "\n";
        
        if (isset($change['modifications'])) {
            $body .= "\nModifications:\n";
            foreach ($change['modifications'] as $type => $details) {
                $body .= "- " . ucfirst(str_replace('_', ' ', $type)) . "\n";
            }
        }
        
        mail($this->config['alert_email'], $subject, $body);
    }
    
    public function updateBaseline() {
        $this->baseline = $this->currentState;
        $this->saveBaseline();
        $this->log("Baseline updated with current file system state.");
    }
    
    private function loadBaseline() {
        if (file_exists($this->baselineFile)) {
            $this->baseline = json_decode(file_get_contents($this->baselineFile), true) ?: [];
        }
    }
    
    private function saveBaseline() {
        $dir = dirname($this->baselineFile);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        file_put_contents($this->baselineFile, json_encode($this->baseline, JSON_PRETTY_PRINT));
    }
    
    private function log($message, $logFile = null) {
        $logFile = $logFile ?: $this->alertsFile;
        $dir = dirname($logFile);
        
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
    }
    
    public function getBaseline() {
        return $this->baseline;
    }
    
    public function getCurrentState() {
        return $this->currentState;
    }
}

?>
