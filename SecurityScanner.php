<?php

class SecurityScanner {
    private $scanResults = [];
    private $suspiciousPatterns = [];
    private $malwareSignatures = [];
    private $config = [];
    
    public function __construct($configFile = 'config/security_config.json') {
        $this->loadConfig($configFile);
        $this->initializePatterns();
        $this->initializeMalwareSignatures();
    }
    
    private function loadConfig($configFile) {
        if (file_exists($configFile)) {
            $this->config = json_decode(file_get_contents($configFile), true);
        } else {
            $this->config = $this->getDefaultConfig();
        }
    }
    
    private function getDefaultConfig() {
        return [
            'scan_directories' => ['./', '../'],
            'exclude_directories' => ['vendor/', 'node_modules/', '.git/'],
            'max_file_size' => 50 * 1024 * 1024, // 50MB
            'suspicious_extensions' => ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'],
            'log_file' => 'logs/security_scan.log',
            'alert_email' => '',
            'quarantine_dir' => 'quarantine/'
        ];
    }
    
    private function initializePatterns() {
        $this->suspiciousPatterns = [
            // PHP backdoors and shells
            '/eval\s*\(\s*\$_(GET|POST|REQUEST|COOKIE)/i',
            '/system\s*\(\s*\$_(GET|POST|REQUEST)/i',
            '/exec\s*\(\s*\$_(GET|POST|REQUEST)/i',
            '/shell_exec\s*\(\s*\$_(GET|POST|REQUEST)/i',
            '/passthru\s*\(\s*\$_(GET|POST|REQUEST)/i',
            '/base64_decode\s*\(\s*\$_(GET|POST|REQUEST)/i',
            '/file_get_contents\s*\(\s*["\']php:\/\/input/i',
            
            // SQL injection patterns
            '/union\s+select/i',
            '/drop\s+table/i',
            '/insert\s+into/i',
            '/delete\s+from/i',
            
            // XSS patterns
            '/<script[^>]*>.*?<\/script>/is',
            '/javascript:/i',
            '/on(load|click|mouseover|error)\s*=/i',
            
            // File inclusion vulnerabilities
            '/include\s*\(\s*\$_(GET|POST|REQUEST)/i',
            '/require\s*\(\s*\$_(GET|POST|REQUEST)/i',
            
            // Suspicious functions
            '/file_put_contents\s*\(/i',
            '/fwrite\s*\(/i',
            '/fputs\s*\(/i',
            '/curl_exec\s*\(/i',
            '/fsockopen\s*\(/i',
            
            // Obfuscated code
            '/\$[a-zA-Z_][a-zA-Z0-9_]*\s*=\s*["\'][a-zA-Z0-9+\/=]{50,}["\']/i',
            '/chr\s*\(\s*\d+\s*\)\s*\./i',
            '/\\\\x[0-9a-f]{2}/i'
        ];
    }
    
    private function initializeMalwareSignatures() {
        $this->malwareSignatures = [
            // Common malware file hashes (MD5)
            'c99shell' => 'd8ae5819a0a2ec8c7e8d1d8b8c5f5e5f',
            'r57shell' => 'b374k2.2',
            'wso_shell' => 'WSO',
            
            // Suspicious strings
            'backdoor_strings' => [
                'c99shell',
                'r57shell',
                'WSO shell',
                'b374k',
                'IndoXploit',
                'FilesMan',
                'Bypass 403',
                'Safe Mode Bypass'
            ]
        ];
    }
    
    public function scanDirectory($directory = './') {
        $this->log("Starting security scan of directory: " . $directory);
        $this->scanResults = [];
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $this->scanFile($file->getPathname());
            }
        }
        
        $this->log("Security scan completed. Found " . count($this->scanResults) . " suspicious items.");
        return $this->scanResults;
    }
    
    private function scanFile($filePath) {
        // Skip excluded directories
        foreach ($this->config['exclude_directories'] as $excludeDir) {
            if (strpos($filePath, $excludeDir) !== false) {
                return;
            }
        }
        
        // Check file size
        $fileSize = filesize($filePath);
        if ($fileSize > $this->config['max_file_size']) {
            return;
        }
        
        $fileName = basename($filePath);
        $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $threats = [];
        
        // Check suspicious file extensions
        if (in_array('.' . $fileExtension, $this->config['suspicious_extensions'])) {
            $threats[] = [
                'type' => 'suspicious_extension',
                'severity' => 'high',
                'description' => 'Suspicious file extension: .' . $fileExtension
            ];
        }
        
        // Check file permissions
        $perms = fileperms($filePath);
        if ($perms & 0x0040) { // World writable
            $threats[] = [
                'type' => 'world_writable',
                'severity' => 'medium',
                'description' => 'File is world writable'
            ];
        }
        
        // Read and analyze file content
        $content = file_get_contents($filePath);
        if ($content === false) {
            return;
        }
        
        // Check for malware signatures
        $this->checkMalwareSignatures($content, $threats);
        
        // Check for suspicious patterns
        $this->checkSuspiciousPatterns($content, $threats);
        
        // Check for recently modified files
        $modTime = filemtime($filePath);
        if ($modTime > time() - 86400) { // Modified in last 24 hours
            $threats[] = [
                'type' => 'recently_modified',
                'severity' => 'low',
                'description' => 'File modified in last 24 hours'
            ];
        }
        
        // Check for hidden files
        if (strpos($fileName, '.') === 0 && $fileName !== '.' && $fileName !== '..') {
            $threats[] = [
                'type' => 'hidden_file',
                'severity' => 'medium',
                'description' => 'Hidden file detected'
            ];
        }
        
        if (!empty($threats)) {
            $this->scanResults[] = [
                'file' => $filePath,
                'size' => $fileSize,
                'modified' => date('Y-m-d H:i:s', $modTime),
                'threats' => $threats,
                'risk_level' => $this->calculateRiskLevel($threats)
            ];
        }
    }
    
    private function checkMalwareSignatures($content, &$threats) {
        // Check for known malware strings
        foreach ($this->malwareSignatures['backdoor_strings'] as $signature) {
            if (stripos($content, $signature) !== false) {
                $threats[] = [
                    'type' => 'malware_signature',
                    'severity' => 'critical',
                    'description' => 'Known malware signature detected: ' . $signature
                ];
            }
        }
        
        // Check file hash against known malware
        $fileHash = md5($content);
        foreach ($this->malwareSignatures as $name => $hash) {
            if (is_string($hash) && $fileHash === $hash) {
                $threats[] = [
                    'type' => 'malware_hash',
                    'severity' => 'critical',
                    'description' => 'File matches known malware hash: ' . $name
                ];
            }
        }
    }
    
    private function checkSuspiciousPatterns($content, &$threats) {
        foreach ($this->suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $threats[] = [
                    'type' => 'suspicious_pattern',
                    'severity' => 'high',
                    'description' => 'Suspicious code pattern detected: ' . substr($matches[0], 0, 100)
                ];
            }
        }
    }
    
    private function calculateRiskLevel($threats) {
        $score = 0;
        foreach ($threats as $threat) {
            switch ($threat['severity']) {
                case 'critical': $score += 10; break;
                case 'high': $score += 7; break;
                case 'medium': $score += 4; break;
                case 'low': $score += 1; break;
            }
        }
        
        if ($score >= 10) return 'critical';
        if ($score >= 7) return 'high';
        if ($score >= 4) return 'medium';
        return 'low';
    }
    
    public function getScanResults() {
        return $this->scanResults;
    }
    
    public function generateReport() {
        $report = [
            'scan_date' => date('Y-m-d H:i:s'),
            'total_threats' => count($this->scanResults),
            'risk_summary' => $this->getRiskSummary(),
            'threats' => $this->scanResults
        ];
        
        return $report;
    }
    
    private function getRiskSummary() {
        $summary = ['critical' => 0, 'high' => 0, 'medium' => 0, 'low' => 0];
        
        foreach ($this->scanResults as $result) {
            $summary[$result['risk_level']]++;
        }
        
        return $summary;
    }
    
    private function log($message) {
        $logDir = dirname($this->config['log_file']);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($this->config['log_file'], "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
    }
    
    public function quarantineFile($filePath) {
        $quarantineDir = $this->config['quarantine_dir'];
        if (!is_dir($quarantineDir)) {
            mkdir($quarantineDir, 0755, true);
        }
        
        $fileName = basename($filePath);
        $quarantinePath = $quarantineDir . date('Y-m-d_H-i-s') . '_' . $fileName;
        
        if (rename($filePath, $quarantinePath)) {
            $this->log("File quarantined: $filePath -> $quarantinePath");
            return true;
        }
        
        return false;
    }
}

?>
