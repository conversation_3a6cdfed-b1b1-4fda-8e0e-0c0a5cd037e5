<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../SecurityScanner.php';
require_once '../FileSystemMonitor.php';
require_once '../ThreatDetectionEngine.php';
require_once '../SecurityReporter.php';

class MobileSecurityAPI {
    private $scanner;
    private $monitor;
    private $threatEngine;
    private $reporter;
    private $apiKey;
    private $deviceId;
    
    public function __construct() {
        $this->scanner = new SecurityScanner();
        $this->monitor = new FileSystemMonitor();
        $this->threatEngine = new ThreatDetectionEngine();
        $this->reporter = new SecurityReporter();
        
        $this->validateRequest();
    }
    
    private function validateRequest() {
        // Check API key
        $headers = getallheaders();
        $this->apiKey = $headers['X-API-Key'] ?? $_GET['api_key'] ?? null;
        $this->deviceId = $headers['X-Device-ID'] ?? $_GET['device_id'] ?? null;
        
        if (!$this->apiKey) {
            $this->sendError('API key required', 401);
        }
        
        // Simple API key validation (in production, use proper authentication)
        if (!$this->isValidApiKey($this->apiKey)) {
            $this->sendError('Invalid API key', 403);
        }
    }
    
    private function isValidApiKey($key) {
        // In production, validate against database
        $validKeys = [
            'mobile_app_key_2024',
            'android_security_scanner',
            'ios_security_scanner'
        ];
        
        return in_array($key, $validKeys);
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $endpoint = $_GET['endpoint'] ?? '';
        
        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($endpoint);
                    break;
                case 'POST':
                    $this->handlePost($endpoint);
                    break;
                case 'PUT':
                    $this->handlePut($endpoint);
                    break;
                case 'DELETE':
                    $this->handleDelete($endpoint);
                    break;
                default:
                    $this->sendError('Method not allowed', 405);
            }
        } catch (Exception $e) {
            $this->sendError($e->getMessage(), 500);
        }
    }
    
    private function handleGet($endpoint) {
        switch ($endpoint) {
            case 'scan/status':
                $this->getScanStatus();
                break;
            case 'scan/results':
                $this->getScanResults();
                break;
            case 'monitor/changes':
                $this->getFileChanges();
                break;
            case 'device/info':
                $this->getDeviceInfo();
                break;
            case 'threats/summary':
                $this->getThreatSummary();
                break;
            case 'reports/latest':
                $this->getLatestReport();
                break;
            default:
                $this->sendError('Endpoint not found', 404);
        }
    }
    
    private function handlePost($endpoint) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        switch ($endpoint) {
            case 'scan/start':
                $this->startScan($input);
                break;
            case 'scan/file':
                $this->scanFile($input);
                break;
            case 'monitor/baseline':
                $this->createBaseline($input);
                break;
            case 'device/register':
                $this->registerDevice($input);
                break;
            case 'alerts/send':
                $this->sendAlert($input);
                break;
            case 'file/quarantine':
                $this->quarantineFile($input);
                break;
            default:
                $this->sendError('Endpoint not found', 404);
        }
    }
    
    private function getScanStatus() {
        $results = $this->scanner->getScanResults();
        $report = $this->scanner->generateReport();
        
        $status = [
            'is_scanning' => false, // Would track actual scanning state
            'last_scan' => $report['scan_date'] ?? null,
            'total_threats' => $report['total_threats'] ?? 0,
            'risk_summary' => $report['risk_summary'] ?? [],
            'device_id' => $this->deviceId,
            'timestamp' => date('c')
        ];
        
        $this->sendSuccess($status);
    }
    
    private function getScanResults() {
        $limit = $_GET['limit'] ?? 50;
        $offset = $_GET['offset'] ?? 0;
        
        $results = $this->scanner->getScanResults();
        $paginatedResults = array_slice($results, $offset, $limit);
        
        $response = [
            'results' => $paginatedResults,
            'total' => count($results),
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => (count($results) > ($offset + $limit))
        ];
        
        $this->sendSuccess($response);
    }
    
    private function getFileChanges() {
        $changes = $this->monitor->checkForChanges();
        $limit = $_GET['limit'] ?? 20;
        
        $response = [
            'changes' => array_slice($changes, 0, $limit),
            'total' => count($changes),
            'timestamp' => date('c')
        ];
        
        $this->sendSuccess($response);
    }
    
    private function getDeviceInfo() {
        $info = [
            'device_id' => $this->deviceId,
            'api_key' => substr($this->apiKey, 0, 8) . '...',
            'server_time' => date('c'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'platform' => $this->detectPlatform(),
            'last_activity' => date('c')
        ];
        
        $this->sendSuccess($info);
    }
    
    private function getThreatSummary() {
        $results = $this->scanner->getScanResults();
        $changes = $this->monitor->checkForChanges();
        
        $threatLevels = ['critical' => 0, 'high' => 0, 'medium' => 0, 'low' => 0];
        foreach ($results as $result) {
            if (isset($threatLevels[$result['risk_level']])) {
                $threatLevels[$result['risk_level']]++;
            }
        }
        
        $recentThreats = array_filter($results, function($r) {
            return strtotime($r['modified']) > (time() - 86400); // Last 24 hours
        });
        
        $summary = [
            'total_threats' => count($results),
            'threat_levels' => $threatLevels,
            'recent_threats' => count($recentThreats),
            'file_changes' => count($changes),
            'risk_score' => $this->calculateOverallRisk($results, $changes),
            'last_updated' => date('c')
        ];
        
        $this->sendSuccess($summary);
    }
    
    private function getLatestReport() {
        $report = $this->scanner->generateReport();
        $this->sendSuccess($report);
    }
    
    private function startScan($input) {
        $directory = $input['directory'] ?? './';
        $scanType = $input['type'] ?? 'quick'; // quick, full, custom
        
        // Log scan start
        $this->reporter->logSecurityEvent('mobile_scan_started', 'info', [
            'device_id' => $this->deviceId,
            'scan_type' => $scanType,
            'directory' => $directory
        ]);
        
        $results = $this->scanner->scanDirectory($directory);
        
        // Log scan completion
        $this->reporter->logSecurityEvent('mobile_scan_completed', 'info', [
            'device_id' => $this->deviceId,
            'threats_found' => count($results),
            'scan_type' => $scanType
        ]);
        
        $response = [
            'scan_id' => uniqid('scan_'),
            'status' => 'completed',
            'results' => $results,
            'summary' => [
                'total_threats' => count($results),
                'critical' => count(array_filter($results, function($r) { return $r['risk_level'] === 'critical'; })),
                'high' => count(array_filter($results, function($r) { return $r['risk_level'] === 'high'; })),
                'medium' => count(array_filter($results, function($r) { return $r['risk_level'] === 'medium'; })),
                'low' => count(array_filter($results, function($r) { return $r['risk_level'] === 'low'; }))
            ],
            'timestamp' => date('c')
        ];
        
        $this->sendSuccess($response);
    }
    
    private function scanFile($input) {
        $filePath = $input['file_path'] ?? '';
        
        if (!$filePath || !file_exists($filePath)) {
            $this->sendError('File not found', 404);
        }
        
        $analysis = $this->threatEngine->analyzeFile($filePath);
        
        $this->reporter->logThreatDetection($analysis);
        
        $this->sendSuccess($analysis);
    }
    
    private function createBaseline($input) {
        $directories = $input['directories'] ?? ['./'];
        
        $count = $this->monitor->createBaseline();
        
        $this->reporter->logSecurityEvent('baseline_created', 'info', [
            'device_id' => $this->deviceId,
            'files_count' => $count,
            'directories' => $directories
        ]);
        
        $response = [
            'status' => 'success',
            'files_count' => $count,
            'timestamp' => date('c')
        ];
        
        $this->sendSuccess($response);
    }
    
    private function registerDevice($input) {
        $deviceInfo = [
            'device_id' => $this->deviceId,
            'platform' => $input['platform'] ?? 'unknown',
            'app_version' => $input['app_version'] ?? '1.0.0',
            'os_version' => $input['os_version'] ?? 'unknown',
            'device_model' => $input['device_model'] ?? 'unknown',
            'registered_at' => date('c'),
            'last_seen' => date('c')
        ];
        
        // In production, save to database
        $this->saveDeviceInfo($deviceInfo);
        
        $this->reporter->logSecurityEvent('device_registered', 'info', $deviceInfo);
        
        $this->sendSuccess(['status' => 'registered', 'device_info' => $deviceInfo]);
    }
    
    private function sendAlert($input) {
        $alertData = [
            'event' => $input['event'] ?? 'mobile_alert',
            'severity' => $input['severity'] ?? 'medium',
            'details' => $input['details'] ?? [],
            'device_id' => $this->deviceId,
            'timestamp' => time()
        ];
        
        $success = $this->reporter->logSecurityEvent(
            $alertData['event'],
            $alertData['severity'],
            $alertData['details']
        );
        
        $this->sendSuccess(['alert_sent' => $success, 'alert_id' => uniqid('alert_')]);
    }
    
    private function quarantineFile($input) {
        $filePath = $input['file_path'] ?? '';
        
        if (!$filePath || !file_exists($filePath)) {
            $this->sendError('File not found', 404);
        }
        
        $success = $this->scanner->quarantineFile($filePath);
        
        if ($success) {
            $this->reporter->logSecurityEvent('file_quarantined', 'high', [
                'file_path' => $filePath,
                'device_id' => $this->deviceId,
                'quarantined_by' => 'mobile_app'
            ]);
        }
        
        $this->sendSuccess(['quarantined' => $success, 'file' => $filePath]);
    }
    
    private function calculateOverallRisk($threats, $changes) {
        $score = 0;
        
        foreach ($threats as $threat) {
            switch ($threat['risk_level']) {
                case 'critical': $score += 25; break;
                case 'high': $score += 15; break;
                case 'medium': $score += 8; break;
                case 'low': $score += 3; break;
            }
        }
        
        foreach ($changes as $change) {
            switch ($change['severity']) {
                case 'high': $score += 10; break;
                case 'medium': $score += 5; break;
                case 'low': $score += 2; break;
            }
        }
        
        return min($score, 100);
    }
    
    private function detectPlatform() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        if (strpos($userAgent, 'iPhone') !== false || strpos($userAgent, 'iPad') !== false) {
            return 'iOS';
        } elseif (strpos($userAgent, 'Android') !== false) {
            return 'Android';
        } elseif (strpos($userAgent, 'Mobile') !== false) {
            return 'Mobile';
        }
        
        return 'Unknown';
    }
    
    private function saveDeviceInfo($deviceInfo) {
        // In production, save to database
        $deviceFile = '../data/devices/' . $this->deviceId . '.json';
        $deviceDir = dirname($deviceFile);
        
        if (!is_dir($deviceDir)) {
            mkdir($deviceDir, 0755, true);
        }
        
        file_put_contents($deviceFile, json_encode($deviceInfo, JSON_PRETTY_PRINT));
    }
    
    private function sendSuccess($data) {
        $response = [
            'success' => true,
            'data' => $data,
            'timestamp' => date('c'),
            'api_version' => '1.0'
        ];
        
        echo json_encode($response);
        exit;
    }
    
    private function sendError($message, $code = 400) {
        http_response_code($code);
        
        $response = [
            'success' => false,
            'error' => [
                'message' => $message,
                'code' => $code
            ],
            'timestamp' => date('c'),
            'api_version' => '1.0'
        ];
        
        echo json_encode($response);
        exit;
    }
}

// Handle the request
$api = new MobileSecurityAPI();
$api->handleRequest();
?>
