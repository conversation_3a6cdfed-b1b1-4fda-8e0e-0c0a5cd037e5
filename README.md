# Security Scanner Application

A comprehensive PHP-based security scanner designed to detect suspicious files, potential hacking attempts, and monitor file system integrity.

## Features

### 🔍 **Security Scanning**
- **Malware Detection**: Identifies known web shells (C99, R57, WSO, B374k)
- **Backdoor Detection**: Detects PHP backdoors and obfuscated code
- **Pattern Analysis**: Scans for suspicious code patterns and vulnerabilities
- **File Analysis**: Checks file permissions, sizes, and modification times
- **Threat Scoring**: Assigns risk scores to detected threats

### 📊 **File System Monitoring**
- **Baseline Creation**: Creates file system snapshots for integrity checking
- **Change Detection**: Monitors file additions, modifications, and deletions
- **Permission Monitoring**: Tracks permission changes and ownership modifications
- **Real-time Alerts**: Immediate notifications for suspicious activities

### 🎯 **Threat Detection Engine**
- **Advanced Pattern Matching**: Uses regex patterns to identify threats
- **Encoding Detection**: Identifies base64, hex, and other encoding techniques
- **Network Pattern Analysis**: Detects suspicious network connections
- **Behavioral Analysis**: Monitors for unusual file and network behavior

### 📈 **Reporting & Logging**
- **Comprehensive Reports**: HTML, JSON, and CSV report formats
- **Alert System**: Email and webhook notifications
- **Log Management**: Automatic log rotation and cleanup
- **Risk Assessment**: Overall security risk scoring

### 🌐 **Web Interface**
- **Dashboard**: Real-time security overview
- **Interactive Scanning**: On-demand security scans
- **Threat Management**: Quarantine and analyze suspicious files
- **Configuration**: Easy setup and customization

## Installation

### Requirements
- PHP 7.4 or higher
- Web server (Apache/Nginx)
- Write permissions for application directories
- Required PHP extensions: json, curl, fileinfo, mbstring

### Quick Setup
1. **Download and Extract**: Place files in your web directory
2. **Run Installer**: Navigate to `install.php` in your browser
3. **Follow Setup**: Complete the installation wizard
4. **Access Dashboard**: Go to `index.php` to start using the scanner

### Manual Installation
```bash
# Create required directories
mkdir logs data reports quarantine config

# Set permissions
chmod 755 logs data reports quarantine config

# Copy configuration
cp config/security_config.json.example config/security_config.json
```

## Usage

### Basic Scanning
```php
require_once 'SecurityScanner.php';

$scanner = new SecurityScanner();
$results = $scanner->scanDirectory('./');

foreach ($results as $threat) {
    echo "Threat found in: " . $threat['file'] . "\n";
    echo "Risk level: " . $threat['risk_level'] . "\n";
}
```

### File System Monitoring
```php
require_once 'FileSystemMonitor.php';

$monitor = new FileSystemMonitor();

// Create baseline
$monitor->createBaseline();

// Check for changes
$changes = $monitor->checkForChanges();
```

### Threat Analysis
```php
require_once 'ThreatDetectionEngine.php';

$engine = new ThreatDetectionEngine();
$analysis = $engine->analyzeFile('suspicious_file.php');

echo "Threat score: " . $analysis['threat_score'] . "/100\n";
echo "Recommended action: " . $analysis['recommended_action'] . "\n";
```

## Configuration

### Security Settings
Edit `config/security_config.json` to customize:

```json
{
    "scanner": {
        "scan_directories": ["./", "../"],
        "exclude_directories": ["vendor/", "node_modules/"],
        "suspicious_extensions": [".exe", ".bat", ".cmd"],
        "max_file_size": 52428800
    },
    "monitor": {
        "check_interval": 300,
        "alert_on_new_files": true,
        "alert_on_modified_files": true
    },
    "reporting": {
        "email_alerts": true,
        "email_recipients": ["<EMAIL>"],
        "retention_days": 30
    }
}
```

### Alert Configuration
Set up email notifications:
```json
{
    "notifications": {
        "email": {
            "smtp_host": "smtp.gmail.com",
            "smtp_port": 587,
            "smtp_username": "<EMAIL>",
            "smtp_password": "your-password",
            "from_email": "<EMAIL>"
        }
    }
}
```

## Security Features

### Threat Detection Capabilities
- **Web Shells**: C99, R57, WSO, B374k, and variants
- **PHP Backdoors**: eval(), exec(), system(), shell_exec() patterns
- **SQL Injection**: UNION SELECT, DROP TABLE patterns
- **XSS Payloads**: Script tags, JavaScript protocols
- **File Inclusion**: LFI/RFI vulnerability patterns
- **Obfuscated Code**: Base64, hex encoding detection

### File System Protection
- **Integrity Monitoring**: MD5 hash verification
- **Permission Tracking**: File permission changes
- **Ownership Monitoring**: User/group ownership changes
- **Size Monitoring**: Unusual file size changes
- **Timestamp Analysis**: Suspicious modification times

## API Reference

### SecurityScanner Class
```php
// Initialize scanner
$scanner = new SecurityScanner($configFile);

// Scan directory
$results = $scanner->scanDirectory($directory);

// Generate report
$report = $scanner->generateReport();

// Quarantine file
$success = $scanner->quarantineFile($filePath);
```

### FileSystemMonitor Class
```php
// Initialize monitor
$monitor = new FileSystemMonitor($config);

// Create baseline
$count = $monitor->createBaseline();

// Check for changes
$changes = $monitor->checkForChanges();

// Update baseline
$monitor->updateBaseline();
```

### ThreatDetectionEngine Class
```php
// Initialize engine
$engine = new ThreatDetectionEngine($config);

// Analyze file
$analysis = $engine->analyzeFile($filePath);

// Analyze behavior
$threats = $engine->analyzeBehavior($logData);

// Update threat database
$engine->updateThreatDatabase($newThreats);
```

## Best Practices

### Regular Maintenance
1. **Update Baselines**: Refresh file system baselines after legitimate changes
2. **Review Logs**: Regularly check security logs for patterns
3. **Update Signatures**: Keep threat signatures up to date
4. **Clean Quarantine**: Periodically review and clean quarantined files

### Security Hardening
1. **Restrict Access**: Limit access to scanner files and directories
2. **Use HTTPS**: Enable SSL/TLS for web interface
3. **Strong Passwords**: Use strong authentication credentials
4. **Regular Updates**: Keep PHP and extensions updated

### Performance Optimization
1. **Exclude Directories**: Exclude unnecessary directories from scanning
2. **File Size Limits**: Set appropriate file size limits
3. **Scan Scheduling**: Schedule intensive scans during off-peak hours
4. **Log Rotation**: Configure automatic log rotation

## Troubleshooting

### Common Issues
- **Permission Errors**: Ensure proper file/directory permissions
- **Memory Limits**: Increase PHP memory limit for large scans
- **Timeout Issues**: Adjust execution time limits
- **False Positives**: Fine-tune detection patterns

### Log Files
- `logs/security_scan.log` - General security events
- `logs/threats.log` - Detected threats
- `logs/file_changes.log` - File system changes
- `logs/alerts.log` - Alert notifications

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the documentation
- Review log files for errors
- Submit issues on GitHub
- Contact the development team

## Changelog

### Version 1.0.0
- Initial release
- Core security scanning functionality
- File system monitoring
- Web interface
- Reporting system
- Alert notifications

---

**⚠️ Security Notice**: This tool is designed to help identify security threats but should not be the only security measure. Always keep your systems updated and follow security best practices.
