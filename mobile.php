<?php
require_once 'SecurityScanner.php';
require_once 'FileSystemMonitor.php';
require_once 'ThreatDetectionEngine.php';

session_start();

// Initialize components
$scanner = new SecurityScanner();
$monitor = new FileSystemMonitor();
$threatEngine = new ThreatDetectionEngine();

// Handle AJAX requests
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'quick_scan':
            $results = $scanner->scanDirectory('./');
            $summary = [
                'total_threats' => count($results),
                'critical' => count(array_filter($results, function($r) { return $r['risk_level'] === 'critical'; })),
                'high' => count(array_filter($results, function($r) { return $r['risk_level'] === 'high'; })),
                'medium' => count(array_filter($results, function($r) { return $r['risk_level'] === 'medium'; })),
                'low' => count(array_filter($results, function($r) { return $r['risk_level'] === 'low'; }))
            ];
            echo json_encode(['success' => true, 'summary' => $summary, 'threats' => array_slice($results, 0, 10)]);
            exit;
            
        case 'device_info':
            $deviceInfo = [
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
                'platform' => $this->detectMobilePlatform(),
                'timestamp' => date('Y-m-d H:i:s')
            ];
            echo json_encode(['success' => true, 'device' => $deviceInfo]);
            exit;
            
        case 'file_check':
            $changes = $monitor->checkForChanges();
            $recentChanges = array_slice($changes, 0, 5);
            echo json_encode(['success' => true, 'changes' => $recentChanges, 'total' => count($changes)]);
            exit;
    }
}

function detectMobilePlatform() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    if (strpos($userAgent, 'iPhone') !== false || strpos($userAgent, 'iPad') !== false) {
        return 'iOS';
    } elseif (strpos($userAgent, 'Android') !== false) {
        return 'Android';
    } elseif (strpos($userAgent, 'Mobile') !== false) {
        return 'Mobile';
    }
    
    return 'Desktop';
}

$platform = detectMobilePlatform();
$recentScans = $scanner->getScanResults();
$scanReport = $scanner->generateReport();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Mobile Security Scanner</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #007bff;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --success-color: #28a745;
            --dark-color: #343a40;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 0;
            margin: 0;
        }
        
        .mobile-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .mobile-container {
            padding: 20px 15px;
            max-width: 100%;
        }
        
        .security-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .threat-indicator {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin: 0 auto 15px;
        }
        
        .threat-critical { background: var(--danger-color); }
        .threat-high { background: #fd7e14; }
        .threat-medium { background: var(--warning-color); color: #000; }
        .threat-low { background: var(--success-color); }
        .threat-safe { background: var(--success-color); }
        
        .scan-button {
            width: 100%;
            padding: 15px;
            font-size: 18px;
            border-radius: 10px;
            border: none;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .scan-button:active {
            transform: scale(0.98);
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        
        .threat-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .threat-item {
            background: rgba(255, 255, 255, 0.8);
            padding: 12px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .threat-item.critical { border-left-color: var(--danger-color); }
        .threat-item.high { border-left-color: #fd7e14; }
        .threat-item.medium { border-left-color: var(--warning-color); }
        .threat-item.low { border-left-color: var(--success-color); }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 10px 0;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .nav-item {
            text-align: center;
            padding: 10px;
            color: #666;
            text-decoration: none;
            font-size: 12px;
            transition: color 0.3s ease;
        }
        
        .nav-item.active, .nav-item:hover {
            color: var(--primary-color);
        }
        
        .nav-item i {
            display: block;
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert-mobile {
            position: fixed;
            top: 80px;
            left: 15px;
            right: 15px;
            z-index: 1001;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .fab {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            border: none;
            font-size: 24px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            z-index: 999;
        }
        
        .tab-content {
            padding-bottom: 80px;
        }
        
        @media (max-width: 576px) {
            .mobile-container {
                padding: 15px 10px;
            }
            
            .security-card {
                padding: 15px;
                margin-bottom: 15px;
            }
            
            .quick-stats {
                grid-template-columns: repeat(4, 1fr);
                gap: 10px;
            }
            
            .stat-card {
                padding: 10px 5px;
            }
            
            .stat-number {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <div class="mobile-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-shield-alt text-primary"></i>
                Security Scanner
            </h5>
            <div class="d-flex align-items-center">
                <span class="badge bg-primary me-2"><?= $platform ?></span>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshData()">
                    <i class="fas fa-sync"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="mobile-container">
        <!-- Security Status Card -->
        <div class="security-card">
            <div class="text-center">
                <div class="threat-indicator threat-safe" id="mainThreatIndicator">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h4 id="securityStatus">System Secure</h4>
                <p class="text-muted mb-3" id="lastScanTime">Last scan: Never</p>
                
                <button class="btn btn-primary scan-button" onclick="startQuickScan()">
                    <i class="fas fa-search me-2"></i>
                    Quick Security Scan
                </button>
                
                <button class="btn btn-outline-info scan-button" onclick="checkFileChanges()">
                    <i class="fas fa-eye me-2"></i>
                    Check File Changes
                </button>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="quick-stats" id="quickStats">
            <div class="stat-card">
                <div class="stat-number text-danger" id="criticalCount">0</div>
                <div class="stat-label">Critical</div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-warning" id="highCount">0</div>
                <div class="stat-label">High</div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-info" id="mediumCount">0</div>
                <div class="stat-label">Medium</div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-success" id="lowCount">0</div>
                <div class="stat-label">Low</div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div class="loading" id="loadingIndicator">
            <div class="spinner"></div>
            <p>Scanning for threats...</p>
        </div>

        <!-- Threats List -->
        <div class="security-card" id="threatsCard" style="display: none;">
            <h6><i class="fas fa-exclamation-triangle text-warning me-2"></i>Detected Threats</h6>
            <div class="threat-list" id="threatsList">
                <!-- Threats will be populated here -->
            </div>
        </div>

        <!-- File Changes -->
        <div class="security-card" id="changesCard" style="display: none;">
            <h6><i class="fas fa-history text-info me-2"></i>Recent File Changes</h6>
            <div id="fileChangesList">
                <!-- File changes will be populated here -->
            </div>
        </div>

        <!-- Device Info -->
        <div class="security-card">
            <h6><i class="fas fa-mobile-alt text-primary me-2"></i>Device Information</h6>
            <div id="deviceInfo">
                <p><strong>Platform:</strong> <?= $platform ?></p>
                <p><strong>IP Address:</strong> <?= $_SERVER['REMOTE_ADDR'] ?? 'Unknown' ?></p>
                <p><strong>Last Check:</strong> <span id="lastCheckTime"><?= date('Y-m-d H:i:s') ?></span></p>
            </div>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button class="fab" onclick="emergencyScan()" title="Emergency Scan">
        <i class="fas fa-exclamation"></i>
    </button>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <div class="row g-0">
            <div class="col">
                <a href="#" class="nav-item active" onclick="showTab('scan')">
                    <i class="fas fa-search"></i>
                    Scan
                </a>
            </div>
            <div class="col">
                <a href="#" class="nav-item" onclick="showTab('monitor')">
                    <i class="fas fa-eye"></i>
                    Monitor
                </a>
            </div>
            <div class="col">
                <a href="#" class="nav-item" onclick="showTab('reports')">
                    <i class="fas fa-chart-bar"></i>
                    Reports
                </a>
            </div>
            <div class="col">
                <a href="#" class="nav-item" onclick="showTab('settings')">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let isScanning = false;
        
        function startQuickScan() {
            if (isScanning) return;
            
            isScanning = true;
            showLoading(true);
            
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=quick_scan'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateSecurityStatus(data.summary);
                    displayThreats(data.threats);
                    showAlert('Scan completed successfully!', 'success');
                }
            })
            .catch(error => {
                showAlert('Scan failed: ' + error.message, 'danger');
            })
            .finally(() => {
                isScanning = false;
                showLoading(false);
                updateLastScanTime();
            });
        }
        
        function checkFileChanges() {
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=file_check'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayFileChanges(data.changes);
                    showAlert(`Found ${data.total} file changes`, 'info');
                }
            });
        }
        
        function updateSecurityStatus(summary) {
            const indicator = document.getElementById('mainThreatIndicator');
            const status = document.getElementById('securityStatus');
            
            // Update counts
            document.getElementById('criticalCount').textContent = summary.critical;
            document.getElementById('highCount').textContent = summary.high;
            document.getElementById('mediumCount').textContent = summary.medium;
            document.getElementById('lowCount').textContent = summary.low;
            
            // Update main indicator
            if (summary.critical > 0) {
                indicator.className = 'threat-indicator threat-critical';
                indicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                status.textContent = 'Critical Threats Detected';
            } else if (summary.high > 0) {
                indicator.className = 'threat-indicator threat-high';
                indicator.innerHTML = '<i class="fas fa-exclamation"></i>';
                status.textContent = 'High Risk Threats Found';
            } else if (summary.medium > 0 || summary.low > 0) {
                indicator.className = 'threat-indicator threat-medium';
                indicator.innerHTML = '<i class="fas fa-info"></i>';
                status.textContent = 'Minor Threats Detected';
            } else {
                indicator.className = 'threat-indicator threat-safe';
                indicator.innerHTML = '<i class="fas fa-shield-alt"></i>';
                status.textContent = 'System Secure';
            }
        }
        
        function displayThreats(threats) {
            const container = document.getElementById('threatsList');
            const card = document.getElementById('threatsCard');
            
            if (threats.length === 0) {
                card.style.display = 'none';
                return;
            }
            
            card.style.display = 'block';
            container.innerHTML = '';
            
            threats.forEach(threat => {
                const item = document.createElement('div');
                item.className = `threat-item ${threat.risk_level}`;
                item.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <strong>${threat.file.split('/').pop()}</strong>
                            <br><small class="text-muted">${threat.file}</small>
                            <div class="mt-1">
                                ${threat.threats.map(t => `<span class="badge bg-secondary me-1">${t.type}</span>`).join('')}
                            </div>
                        </div>
                        <span class="badge bg-${getRiskColor(threat.risk_level)}">${threat.risk_level.toUpperCase()}</span>
                    </div>
                `;
                container.appendChild(item);
            });
        }
        
        function displayFileChanges(changes) {
            const container = document.getElementById('fileChangesList');
            const card = document.getElementById('changesCard');
            
            if (changes.length === 0) {
                card.style.display = 'none';
                return;
            }
            
            card.style.display = 'block';
            container.innerHTML = '';
            
            changes.forEach(change => {
                const item = document.createElement('div');
                item.className = 'alert alert-sm alert-info mb-2';
                item.innerHTML = `
                    <strong>${change.type.replace('_', ' ').toUpperCase()}:</strong> 
                    ${change.file.split('/').pop()}
                    <br><small>Severity: ${change.severity}</small>
                `;
                container.appendChild(item);
            });
        }
        
        function emergencyScan() {
            if (confirm('Perform emergency security scan? This may take longer.')) {
                startQuickScan();
            }
        }
        
        function showLoading(show) {
            const loading = document.getElementById('loadingIndicator');
            loading.className = show ? 'loading show' : 'loading';
        }
        
        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-mobile alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }
        
        function updateLastScanTime() {
            document.getElementById('lastScanTime').textContent = 'Last scan: ' + new Date().toLocaleString();
            document.getElementById('lastCheckTime').textContent = new Date().toLocaleString();
        }
        
        function getRiskColor(risk) {
            switch(risk) {
                case 'critical': return 'danger';
                case 'high': return 'warning';
                case 'medium': return 'info';
                case 'low': return 'success';
                default: return 'secondary';
            }
        }
        
        function showTab(tab) {
            // Update navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Show appropriate content based on tab
            switch(tab) {
                case 'scan':
                    // Already showing scan interface
                    break;
                case 'monitor':
                    checkFileChanges();
                    break;
                case 'reports':
                    showAlert('Reports feature coming soon!', 'info');
                    break;
                case 'settings':
                    showAlert('Settings feature coming soon!', 'info');
                    break;
            }
        }
        
        function refreshData() {
            updateLastScanTime();
            showAlert('Data refreshed', 'success');
        }
        
        // Auto-refresh every 5 minutes
        setInterval(() => {
            if (!isScanning) {
                checkFileChanges();
            }
        }, 300000);
        
        // Initialize on load
        document.addEventListener('DOMContentLoaded', function() {
            updateLastScanTime();
        });
    </script>
</body>
</html>
