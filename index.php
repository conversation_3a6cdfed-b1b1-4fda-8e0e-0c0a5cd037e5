<?php
require_once 'SecurityScanner.php';
require_once 'FileSystemMonitor.php';
require_once 'ThreatDetectionEngine.php';

session_start();

// Initialize components
$scanner = new SecurityScanner();
$monitor = new FileSystemMonitor();
$threatEngine = new ThreatDetectionEngine();

// Handle AJAX requests
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'scan':
            $directory = $_POST['directory'] ?? './';
            $results = $scanner->scanDirectory($directory);
            echo json_encode(['success' => true, 'results' => $results]);
            exit;
            
        case 'monitor_check':
            $changes = $monitor->checkForChanges();
            echo json_encode(['success' => true, 'changes' => $changes]);
            exit;
            
        case 'create_baseline':
            $count = $monitor->createBaseline();
            echo json_encode(['success' => true, 'files_count' => $count]);
            exit;
            
        case 'analyze_file':
            $filePath = $_POST['file_path'] ?? '';
            if (file_exists($filePath)) {
                $analysis = $threatEngine->analyzeFile($filePath);
                echo json_encode(['success' => true, 'analysis' => $analysis]);
            } else {
                echo json_encode(['success' => false, 'error' => 'File not found']);
            }
            exit;
            
        case 'quarantine_file':
            $filePath = $_POST['file_path'] ?? '';
            $success = $scanner->quarantineFile($filePath);
            echo json_encode(['success' => $success]);
            exit;
    }
}

// Get recent scan results
$recentScans = $scanner->getScanResults();
$scanReport = $scanner->generateReport();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Scanner Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .threat-critical { background-color: #dc3545; color: white; }
        .threat-high { background-color: #fd7e14; color: white; }
        .threat-medium { background-color: #ffc107; color: black; }
        .threat-low { background-color: #28a745; color: white; }
        .scan-progress { display: none; }
        .alert-box { margin: 10px 0; }
        .file-item { border: 1px solid #ddd; margin: 5px 0; padding: 10px; border-radius: 5px; }
        .threat-badge { font-size: 0.8em; padding: 3px 8px; border-radius: 12px; margin: 2px; }
    </style>
</head>
<body>
    <nav class="navbar navbar-dark bg-dark">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="fas fa-shield-alt"></i> Security Scanner Dashboard
            </span>
            <div class="navbar-nav">
                <span class="nav-link text-light">
                    <i class="fas fa-clock"></i> Last Scan: <?= date('Y-m-d H:i:s') ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Control Panel -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs"></i> Control Panel</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="scanDirectory" class="form-label">Scan Directory:</label>
                            <input type="text" class="form-control" id="scanDirectory" value="./" placeholder="Enter directory path">
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="startScan()">
                                <i class="fas fa-search"></i> Start Security Scan
                            </button>
                            <button class="btn btn-info" onclick="checkFileChanges()">
                                <i class="fas fa-eye"></i> Check File Changes
                            </button>
                            <button class="btn btn-warning" onclick="createBaseline()">
                                <i class="fas fa-database"></i> Create Baseline
                            </button>
                            <button class="btn btn-secondary" onclick="refreshDashboard()">
                                <i class="fas fa-sync"></i> Refresh Dashboard
                            </button>
                        </div>
                        
                        <div class="scan-progress mt-3">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 100%"></div>
                            </div>
                            <small class="text-muted">Scanning in progress...</small>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> Security Overview</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border rounded p-2 mb-2">
                                    <h4 class="text-danger"><?= $scanReport['risk_summary']['critical'] ?? 0 ?></h4>
                                    <small>Critical</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-2 mb-2">
                                    <h4 class="text-warning"><?= $scanReport['risk_summary']['high'] ?? 0 ?></h4>
                                    <small>High</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-2">
                                    <h4 class="text-info"><?= $scanReport['risk_summary']['medium'] ?? 0 ?></h4>
                                    <small>Medium</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-2">
                                    <h4 class="text-success"><?= $scanReport['risk_summary']['low'] ?? 0 ?></h4>
                                    <small>Low</small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <p class="mb-1"><strong>Total Threats:</strong> <?= $scanReport['total_threats'] ?? 0 ?></p>
                        <p class="mb-0"><strong>Last Scan:</strong> <?= $scanReport['scan_date'] ?? 'Never' ?></p>
                    </div>
                </div>
            </div>

            <!-- Results Panel -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-exclamation-triangle"></i> Security Threats</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="exportResults()">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                    <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                        <div id="scanResults">
                            <?php if (!empty($recentScans)): ?>
                                <?php foreach ($recentScans as $result): ?>
                                    <div class="file-item">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">
                                                    <i class="fas fa-file"></i> 
                                                    <?= htmlspecialchars(basename($result['file'])) ?>
                                                </h6>
                                                <small class="text-muted"><?= htmlspecialchars($result['file']) ?></small>
                                                <div class="mt-2">
                                                    <?php foreach ($result['threats'] as $threat): ?>
                                                        <span class="threat-badge threat-<?= $threat['severity'] ?>">
                                                            <?= htmlspecialchars($threat['description']) ?>
                                                        </span>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge threat-<?= $result['risk_level'] ?> mb-2">
                                                    <?= strtoupper($result['risk_level']) ?>
                                                </span>
                                                <br>
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <button class="btn btn-outline-info btn-sm" 
                                                            onclick="analyzeFile('<?= htmlspecialchars($result['file']) ?>')">
                                                        <i class="fas fa-search"></i> Analyze
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-sm" 
                                                            onclick="quarantineFile('<?= htmlspecialchars($result['file']) ?>')">
                                                        <i class="fas fa-lock"></i> Quarantine
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                Size: <?= number_format($result['size']) ?> bytes | 
                                                Modified: <?= $result['modified'] ?>
                                            </small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center text-muted py-5">
                                    <i class="fas fa-shield-alt fa-3x mb-3"></i>
                                    <h5>No threats detected</h5>
                                    <p>Run a security scan to check for potential threats.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Changes Monitor -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history"></i> File System Changes</h5>
                    </div>
                    <div class="card-body">
                        <div id="fileChanges">
                            <div class="text-center text-muted py-3">
                                <p>Click "Check File Changes" to monitor file system modifications.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analysis Modal -->
    <div class="modal fade" id="analysisModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Threat Analysis</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="analysisContent">
                    <!-- Analysis content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function startScan() {
            const directory = document.getElementById('scanDirectory').value;
            const progressDiv = document.querySelector('.scan-progress');
            
            progressDiv.style.display = 'block';
            
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=scan&directory=${encodeURIComponent(directory)}`
            })
            .then(response => response.json())
            .then(data => {
                progressDiv.style.display = 'none';
                if (data.success) {
                    displayScanResults(data.results);
                    showAlert('Scan completed successfully!', 'success');
                } else {
                    showAlert('Scan failed: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                progressDiv.style.display = 'none';
                showAlert('Error: ' + error.message, 'danger');
            });
        }

        function checkFileChanges() {
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=monitor_check'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayFileChanges(data.changes);
                    showAlert(`Found ${data.changes.length} file changes`, 'info');
                }
            });
        }

        function createBaseline() {
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=create_baseline'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`Baseline created with ${data.files_count} files`, 'success');
                }
            });
        }

        function analyzeFile(filePath) {
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=analyze_file&file_path=${encodeURIComponent(filePath)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayAnalysis(data.analysis);
                } else {
                    showAlert('Analysis failed: ' + data.error, 'danger');
                }
            });
        }

        function quarantineFile(filePath) {
            if (confirm('Are you sure you want to quarantine this file?')) {
                fetch('', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                    body: `action=quarantine_file&file_path=${encodeURIComponent(filePath)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('File quarantined successfully', 'success');
                        refreshDashboard();
                    } else {
                        showAlert('Failed to quarantine file', 'danger');
                    }
                });
            }
        }

        function displayScanResults(results) {
            const container = document.getElementById('scanResults');
            if (results.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-shield-alt fa-3x mb-3 text-success"></i>
                        <h5>No threats detected</h5>
                        <p>Your system appears to be clean.</p>
                    </div>
                `;
                return;
            }

            let html = '';
            results.forEach(result => {
                html += `
                    <div class="file-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <i class="fas fa-file"></i> ${escapeHtml(result.file.split('/').pop())}
                                </h6>
                                <small class="text-muted">${escapeHtml(result.file)}</small>
                                <div class="mt-2">
                `;
                
                result.threats.forEach(threat => {
                    html += `<span class="threat-badge threat-${threat.severity}">${escapeHtml(threat.description)}</span>`;
                });
                
                html += `
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge threat-${result.risk_level} mb-2">${result.risk_level.toUpperCase()}</span>
                                <br>
                                <div class="btn-group-vertical btn-group-sm">
                                    <button class="btn btn-outline-info btn-sm" onclick="analyzeFile('${escapeHtml(result.file)}')">
                                        <i class="fas fa-search"></i> Analyze
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="quarantineFile('${escapeHtml(result.file)}')">
                                        <i class="fas fa-lock"></i> Quarantine
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                Size: ${result.size.toLocaleString()} bytes | Modified: ${result.modified}
                            </small>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function displayFileChanges(changes) {
            const container = document.getElementById('fileChanges');
            if (changes.length === 0) {
                container.innerHTML = '<div class="text-center text-muted py-3"><p>No file changes detected.</p></div>';
                return;
            }

            let html = '';
            changes.forEach(change => {
                const severityClass = change.severity === 'high' ? 'danger' : 
                                    change.severity === 'medium' ? 'warning' : 'info';
                html += `
                    <div class="alert alert-${severityClass} alert-dismissible">
                        <strong>${change.type.replace('_', ' ').toUpperCase()}:</strong> 
                        ${escapeHtml(change.file)}
                        <br><small>Severity: ${change.severity} | Time: ${new Date(change.timestamp * 1000).toLocaleString()}</small>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function displayAnalysis(analysis) {
            let html = `
                <h6>File: ${escapeHtml(analysis.file)}</h6>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>Threat Score:</strong> ${analysis.threat_score}/100
                    </div>
                    <div class="col-md-6">
                        <strong>Threat Level:</strong> 
                        <span class="badge threat-${analysis.threat_level}">${analysis.threat_level.toUpperCase()}</span>
                    </div>
                </div>
                <div class="mb-3">
                    <strong>Recommended Action:</strong> ${analysis.recommended_action.replace('_', ' ').toUpperCase()}
                </div>
            `;

            if (analysis.threats_detected.length > 0) {
                html += '<h6>Threats Detected:</h6><ul>';
                analysis.threats_detected.forEach(threat => {
                    html += `
                        <li>
                            <strong>${threat.category}/${threat.name}</strong> 
                            (Score: ${threat.risk_score}) - ${threat.description}
                            ${threat.matches ? '<br><small>Matches: ' + threat.matches.join(', ') + '</small>' : ''}
                        </li>
                    `;
                });
                html += '</ul>';
            }

            document.getElementById('analysisContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('analysisModal')).show();
        }

        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        function refreshDashboard() {
            location.reload();
        }

        function exportResults() {
            // Implementation for exporting results
            showAlert('Export functionality coming soon!', 'info');
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Auto-refresh every 5 minutes
        setInterval(() => {
            checkFileChanges();
        }, 300000);
    </script>
</body>
</html>
