package com.securityscanner;

import android.Manifest;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Environment;
import android.provider.Settings;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.ArrayList;
import java.util.List;

public class MainActivity extends AppCompatActivity {
    
    private static final int PERMISSION_REQUEST_CODE = 1001;
    private static final String[] REQUIRED_PERMISSIONS = {
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE,
        Manifest.permission.INTERNET,
        Manifest.permission.ACCESS_NETWORK_STATE
    };
    
    // UI Components
    private TextView statusText;
    private TextView threatCountText;
    private MaterialCardView securityStatusCard;
    private Button scanButton;
    private Button monitorButton;
    private ProgressBar scanProgress;
    private RecyclerView threatsRecyclerView;
    private FloatingActionButton emergencyFab;
    
    // Security Components
    private SecurityScanner securityScanner;
    private FileSystemMonitor fileMonitor;
    private ApiClient apiClient;
    private ThreatAdapter threatAdapter;
    
    // Data
    private List<ThreatItem> threatList = new ArrayList<>();
    private boolean isScanning = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initializeViews();
        initializeComponents();
        checkPermissions();
        setupClickListeners();
        updateUI();
    }
    
    private void initializeViews() {
        statusText = findViewById(R.id.statusText);
        threatCountText = findViewById(R.id.threatCountText);
        securityStatusCard = findViewById(R.id.securityStatusCard);
        scanButton = findViewById(R.id.scanButton);
        monitorButton = findViewById(R.id.monitorButton);
        scanProgress = findViewById(R.id.scanProgress);
        threatsRecyclerView = findViewById(R.id.threatsRecyclerView);
        emergencyFab = findViewById(R.id.emergencyFab);
        
        // Setup RecyclerView
        threatAdapter = new ThreatAdapter(threatList, this::onThreatItemClick);
        threatsRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        threatsRecyclerView.setAdapter(threatAdapter);
    }
    
    private void initializeComponents() {
        securityScanner = new SecurityScanner(this);
        fileMonitor = new FileSystemMonitor(this);
        apiClient = new ApiClient(this);
        
        // Register device with API
        apiClient.registerDevice();
    }
    
    private void checkPermissions() {
        List<String> missingPermissions = new ArrayList<>();
        
        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(this, permission) 
                != PackageManager.PERMISSION_GRANTED) {
                missingPermissions.add(permission);
            }
        }
        
        if (!missingPermissions.isEmpty()) {
            ActivityCompat.requestPermissions(this, 
                missingPermissions.toArray(new String[0]), 
                PERMISSION_REQUEST_CODE);
        }
        
        // Check for special permissions
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            if (!Environment.isExternalStorageManager()) {
                showStoragePermissionDialog();
            }
        }
    }
    
    private void showStoragePermissionDialog() {
        new AlertDialog.Builder(this)
            .setTitle("Storage Permission Required")
            .setMessage("This app needs access to device storage to scan for security threats.")
            .setPositiveButton("Grant Permission", (dialog, which) -> {
                Intent intent = new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
                startActivity(intent);
            })
            .setNegativeButton("Cancel", null)
            .show();
    }
    
    private void setupClickListeners() {
        scanButton.setOnClickListener(v -> startSecurityScan());
        monitorButton.setOnClickListener(v -> startFileMonitoring());
        emergencyFab.setOnClickListener(v -> performEmergencyScan());
        
        securityStatusCard.setOnClickListener(v -> showDetailedStatus());
    }
    
    private void startSecurityScan() {
        if (isScanning) {
            Toast.makeText(this, "Scan already in progress", Toast.LENGTH_SHORT).show();
            return;
        }
        
        isScanning = true;
        updateScanningUI(true);
        
        securityScanner.startScan(new SecurityScanner.ScanCallback() {
            @Override
            public void onScanStarted() {
                runOnUiThread(() -> {
                    statusText.setText("Scanning for threats...");
                    scanProgress.setVisibility(View.VISIBLE);
                });
            }
            
            @Override
            public void onScanProgress(int progress, String currentFile) {
                runOnUiThread(() -> {
                    scanProgress.setProgress(progress);
                    statusText.setText("Scanning: " + currentFile);
                });
            }
            
            @Override
            public void onScanCompleted(List<ThreatItem> threats) {
                runOnUiThread(() -> {
                    isScanning = false;
                    updateScanningUI(false);
                    updateThreats(threats);
                    updateSecurityStatus(threats);
                    
                    // Send results to API
                    apiClient.uploadScanResults(threats);
                    
                    Toast.makeText(MainActivity.this, 
                        "Scan completed. Found " + threats.size() + " threats", 
                        Toast.LENGTH_LONG).show();
                });
            }
            
            @Override
            public void onScanError(String error) {
                runOnUiThread(() -> {
                    isScanning = false;
                    updateScanningUI(false);
                    statusText.setText("Scan failed: " + error);
                    Toast.makeText(MainActivity.this, "Scan error: " + error, 
                        Toast.LENGTH_LONG).show();
                });
            }
        });
    }
    
    private void startFileMonitoring() {
        fileMonitor.startMonitoring(new FileSystemMonitor.MonitorCallback() {
            @Override
            public void onFileChanged(String filePath, String changeType) {
                runOnUiThread(() -> {
                    showFileChangeAlert(filePath, changeType);
                });
            }
            
            @Override
            public void onSuspiciousActivity(String description, int severity) {
                runOnUiThread(() -> {
                    showSuspiciousActivityAlert(description, severity);
                });
            }
        });
        
        Toast.makeText(this, "File monitoring started", Toast.LENGTH_SHORT).show();
    }
    
    private void performEmergencyScan() {
        new AlertDialog.Builder(this)
            .setTitle("Emergency Security Scan")
            .setMessage("This will perform a comprehensive security scan. Continue?")
            .setPositiveButton("Yes", (dialog, which) -> {
                startSecurityScan();
                // Also trigger immediate file monitoring
                startFileMonitoring();
            })
            .setNegativeButton("Cancel", null)
            .show();
    }
    
    private void showDetailedStatus() {
        Intent intent = new Intent(this, DetailedStatusActivity.class);
        intent.putExtra("threat_count", threatList.size());
        startActivity(intent);
    }
    
    private void updateScanningUI(boolean scanning) {
        scanButton.setEnabled(!scanning);
        scanProgress.setVisibility(scanning ? View.VISIBLE : View.GONE);
        
        if (!scanning) {
            scanProgress.setProgress(0);
        }
    }
    
    private void updateThreats(List<ThreatItem> threats) {
        threatList.clear();
        threatList.addAll(threats);
        threatAdapter.notifyDataSetChanged();
        
        threatCountText.setText(String.valueOf(threats.size()));
    }
    
    private void updateSecurityStatus(List<ThreatItem> threats) {
        int criticalCount = 0;
        int highCount = 0;
        
        for (ThreatItem threat : threats) {
            if (threat.getSeverity() == ThreatSeverity.CRITICAL) {
                criticalCount++;
            } else if (threat.getSeverity() == ThreatSeverity.HIGH) {
                highCount++;
            }
        }
        
        if (criticalCount > 0) {
            statusText.setText("CRITICAL THREATS DETECTED");
            statusText.setTextColor(ContextCompat.getColor(this, R.color.threat_critical));
            securityStatusCard.setCardBackgroundColor(
                ContextCompat.getColor(this, R.color.threat_critical_bg));
        } else if (highCount > 0) {
            statusText.setText("HIGH RISK THREATS FOUND");
            statusText.setTextColor(ContextCompat.getColor(this, R.color.threat_high));
            securityStatusCard.setCardBackgroundColor(
                ContextCompat.getColor(this, R.color.threat_high_bg));
        } else if (threats.size() > 0) {
            statusText.setText("MINOR THREATS DETECTED");
            statusText.setTextColor(ContextCompat.getColor(this, R.color.threat_medium));
            securityStatusCard.setCardBackgroundColor(
                ContextCompat.getColor(this, R.color.threat_medium_bg));
        } else {
            statusText.setText("SYSTEM SECURE");
            statusText.setTextColor(ContextCompat.getColor(this, R.color.threat_safe));
            securityStatusCard.setCardBackgroundColor(
                ContextCompat.getColor(this, R.color.threat_safe_bg));
        }
    }
    
    private void updateUI() {
        statusText.setText("Ready to scan");
        threatCountText.setText("0");
        securityStatusCard.setCardBackgroundColor(
            ContextCompat.getColor(this, R.color.threat_safe_bg));
    }
    
    private void showFileChangeAlert(String filePath, String changeType) {
        new AlertDialog.Builder(this)
            .setTitle("File System Change Detected")
            .setMessage("File: " + filePath + "\nChange: " + changeType)
            .setPositiveButton("Scan File", (dialog, which) -> {
                securityScanner.scanSpecificFile(filePath);
            })
            .setNegativeButton("Ignore", null)
            .show();
    }
    
    private void showSuspiciousActivityAlert(String description, int severity) {
        String title = severity >= 8 ? "CRITICAL SECURITY ALERT" : "Security Alert";
        
        new AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(description)
            .setPositiveButton("Full Scan", (dialog, which) -> startSecurityScan())
            .setNegativeButton("Dismiss", null)
            .show();
    }
    
    private void onThreatItemClick(ThreatItem threat) {
        Intent intent = new Intent(this, ThreatDetailActivity.class);
        intent.putExtra("threat", threat);
        startActivity(intent);
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, 
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            
            if (!allGranted) {
                new AlertDialog.Builder(this)
                    .setTitle("Permissions Required")
                    .setMessage("This app requires storage and network permissions to function properly.")
                    .setPositiveButton("Grant", (dialog, which) -> checkPermissions())
                    .setNegativeButton("Exit", (dialog, which) -> finish())
                    .show();
            }
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // Refresh security status
        apiClient.getScanStatus();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (fileMonitor != null) {
            fileMonitor.stopMonitoring();
        }
    }
}
