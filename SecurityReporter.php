<?php

class SecurityReporter {
    private $config = [];
    private $logFiles = [];
    private $alertHandlers = [];
    
    public function __construct($config = []) {
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->initializeLogFiles();
        $this->initializeAlertHandlers();
    }
    
    private function getDefaultConfig() {
        return [
            'log_directory' => 'logs/',
            'report_directory' => 'reports/',
            'email_alerts' => true,
            'email_recipients' => [],
            'sms_alerts' => false,
            'webhook_url' => '',
            'retention_days' => 30,
            'max_log_size' => 10 * 1024 * 1024, // 10MB
            'alert_cooldown' => 300, // 5 minutes
            'report_formats' => ['html', 'json', 'csv']
        ];
    }
    
    private function initializeLogFiles() {
        $logDir = $this->config['log_directory'];
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $this->logFiles = [
            'security' => $logDir . 'security.log',
            'threats' => $logDir . 'threats.log',
            'file_changes' => $logDir . 'file_changes.log',
            'alerts' => $logDir . 'alerts.log',
            'system' => $logDir . 'system.log'
        ];
    }
    
    private function initializeAlertHandlers() {
        $this->alertHandlers = [
            'email' => [$this, 'sendEmailAlert'],
            'webhook' => [$this, 'sendWebhookAlert'],
            'log' => [$this, 'logAlert']
        ];
    }
    
    public function logSecurityEvent($event, $severity = 'info', $details = []) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'severity' => $severity,
            'event' => $event,
            'details' => $details,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'session_id' => session_id()
        ];
        
        $this->writeLog('security', $logEntry);
        
        // Trigger alerts for high severity events
        if (in_array($severity, ['critical', 'high'])) {
            $this->triggerAlert($logEntry);
        }
        
        return $logEntry;
    }
    
    public function logThreatDetection($threatData) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'file' => $threatData['file'],
            'threat_score' => $threatData['threat_score'],
            'threat_level' => $threatData['threat_level'],
            'threats' => $threatData['threats_detected'],
            'action_taken' => $threatData['recommended_action']
        ];
        
        $this->writeLog('threats', $logEntry);
        
        // Auto-alert for critical threats
        if ($threatData['threat_level'] === 'critical') {
            $this->triggerAlert([
                'event' => 'critical_threat_detected',
                'severity' => 'critical',
                'details' => $logEntry
            ]);
        }
        
        return $logEntry;
    }
    
    public function logFileChange($changeData) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'type' => $changeData['type'],
            'file' => $changeData['file'],
            'severity' => $changeData['severity'],
            'details' => $changeData['details'] ?? []
        ];
        
        $this->writeLog('file_changes', $logEntry);
        
        return $logEntry;
    }
    
    private function writeLog($logType, $data) {
        if (!isset($this->logFiles[$logType])) {
            return false;
        }
        
        $logFile = $this->logFiles[$logType];
        
        // Check log rotation
        if (file_exists($logFile) && filesize($logFile) > $this->config['max_log_size']) {
            $this->rotateLog($logFile);
        }
        
        $logLine = json_encode($data) . "\n";
        return file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX) !== false;
    }
    
    private function rotateLog($logFile) {
        $timestamp = date('Y-m-d_H-i-s');
        $rotatedFile = $logFile . '.' . $timestamp;
        
        if (rename($logFile, $rotatedFile)) {
            // Compress old log file
            if (function_exists('gzopen')) {
                $this->compressLogFile($rotatedFile);
            }
        }
    }
    
    private function compressLogFile($logFile) {
        $compressedFile = $logFile . '.gz';
        $input = fopen($logFile, 'rb');
        $output = gzopen($compressedFile, 'wb9');
        
        if ($input && $output) {
            while (!feof($input)) {
                gzwrite($output, fread($input, 8192));
            }
            fclose($input);
            gzclose($output);
            unlink($logFile); // Remove original file
        }
    }
    
    public function generateSecurityReport($startDate = null, $endDate = null, $format = 'html') {
        $startDate = $startDate ?: date('Y-m-d', strtotime('-7 days'));
        $endDate = $endDate ?: date('Y-m-d');
        
        $reportData = [
            'report_id' => uniqid('report_'),
            'generated_at' => date('Y-m-d H:i:s'),
            'period' => ['start' => $startDate, 'end' => $endDate],
            'summary' => $this->generateSummary($startDate, $endDate),
            'threats' => $this->getThreatsInPeriod($startDate, $endDate),
            'file_changes' => $this->getFileChangesInPeriod($startDate, $endDate),
            'security_events' => $this->getSecurityEventsInPeriod($startDate, $endDate),
            'recommendations' => $this->generateRecommendations($startDate, $endDate)
        ];
        
        return $this->formatReport($reportData, $format);
    }
    
    private function generateSummary($startDate, $endDate) {
        $threats = $this->getThreatsInPeriod($startDate, $endDate);
        $fileChanges = $this->getFileChangesInPeriod($startDate, $endDate);
        $securityEvents = $this->getSecurityEventsInPeriod($startDate, $endDate);
        
        $threatLevels = ['critical' => 0, 'high' => 0, 'medium' => 0, 'low' => 0];
        foreach ($threats as $threat) {
            if (isset($threatLevels[$threat['threat_level']])) {
                $threatLevels[$threat['threat_level']]++;
            }
        }
        
        $changeTypes = ['new_file' => 0, 'modified_file' => 0, 'deleted_file' => 0];
        foreach ($fileChanges as $change) {
            if (isset($changeTypes[$change['type']])) {
                $changeTypes[$change['type']]++;
            }
        }
        
        return [
            'total_threats' => count($threats),
            'threat_levels' => $threatLevels,
            'total_file_changes' => count($fileChanges),
            'change_types' => $changeTypes,
            'total_security_events' => count($securityEvents),
            'risk_score' => $this->calculateOverallRiskScore($threats, $fileChanges, $securityEvents)
        ];
    }
    
    private function getThreatsInPeriod($startDate, $endDate) {
        return $this->getLogEntriesInPeriod('threats', $startDate, $endDate);
    }
    
    private function getFileChangesInPeriod($startDate, $endDate) {
        return $this->getLogEntriesInPeriod('file_changes', $startDate, $endDate);
    }
    
    private function getSecurityEventsInPeriod($startDate, $endDate) {
        return $this->getLogEntriesInPeriod('security', $startDate, $endDate);
    }
    
    private function getLogEntriesInPeriod($logType, $startDate, $endDate) {
        $logFile = $this->logFiles[$logType];
        $entries = [];
        
        if (!file_exists($logFile)) {
            return $entries;
        }
        
        $handle = fopen($logFile, 'r');
        if ($handle) {
            while (($line = fgets($handle)) !== false) {
                $entry = json_decode(trim($line), true);
                if ($entry && isset($entry['timestamp'])) {
                    $entryDate = date('Y-m-d', strtotime($entry['timestamp']));
                    if ($entryDate >= $startDate && $entryDate <= $endDate) {
                        $entries[] = $entry;
                    }
                }
            }
            fclose($handle);
        }
        
        return $entries;
    }
    
    private function calculateOverallRiskScore($threats, $fileChanges, $securityEvents) {
        $score = 0;
        
        // Threat scoring
        foreach ($threats as $threat) {
            switch ($threat['threat_level']) {
                case 'critical': $score += 25; break;
                case 'high': $score += 15; break;
                case 'medium': $score += 8; break;
                case 'low': $score += 3; break;
            }
        }
        
        // File change scoring
        foreach ($fileChanges as $change) {
            switch ($change['severity']) {
                case 'high': $score += 10; break;
                case 'medium': $score += 5; break;
                case 'low': $score += 2; break;
            }
        }
        
        // Security event scoring
        foreach ($securityEvents as $event) {
            switch ($event['severity']) {
                case 'critical': $score += 20; break;
                case 'high': $score += 12; break;
                case 'medium': $score += 6; break;
                case 'low': $score += 2; break;
            }
        }
        
        return min($score, 100); // Cap at 100
    }
    
    private function generateRecommendations($startDate, $endDate) {
        $threats = $this->getThreatsInPeriod($startDate, $endDate);
        $fileChanges = $this->getFileChangesInPeriod($startDate, $endDate);
        $recommendations = [];
        
        // Analyze threat patterns
        $criticalThreats = array_filter($threats, function($t) { return $t['threat_level'] === 'critical'; });
        if (count($criticalThreats) > 0) {
            $recommendations[] = [
                'priority' => 'critical',
                'title' => 'Immediate Action Required',
                'description' => 'Critical threats detected. Quarantine affected files immediately.',
                'action' => 'quarantine_files'
            ];
        }
        
        // Check for suspicious file changes
        $suspiciousChanges = array_filter($fileChanges, function($c) { 
            return $c['severity'] === 'high' && $c['type'] === 'new_file'; 
        });
        if (count($suspiciousChanges) > 5) {
            $recommendations[] = [
                'priority' => 'high',
                'title' => 'Unusual File Activity',
                'description' => 'Multiple suspicious files created. Review file system integrity.',
                'action' => 'review_file_changes'
            ];
        }
        
        // General security recommendations
        $recommendations[] = [
            'priority' => 'medium',
            'title' => 'Regular Security Maintenance',
            'description' => 'Update file baseline and review security configurations.',
            'action' => 'update_baseline'
        ];
        
        return $recommendations;
    }
    
    private function formatReport($reportData, $format) {
        $reportDir = $this->config['report_directory'];
        if (!is_dir($reportDir)) {
            mkdir($reportDir, 0755, true);
        }
        
        $filename = $reportDir . 'security_report_' . $reportData['report_id'] . '.' . $format;
        
        switch ($format) {
            case 'html':
                return $this->generateHtmlReport($reportData, $filename);
            case 'json':
                return $this->generateJsonReport($reportData, $filename);
            case 'csv':
                return $this->generateCsvReport($reportData, $filename);
            default:
                return false;
        }
    }
    
    private function generateHtmlReport($data, $filename) {
        $html = $this->getHtmlReportTemplate($data);
        file_put_contents($filename, $html);
        return $filename;
    }
    
    private function generateJsonReport($data, $filename) {
        file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT));
        return $filename;
    }
    
    private function generateCsvReport($data, $filename) {
        $csv = fopen($filename, 'w');
        
        // Write threats
        fputcsv($csv, ['Type', 'Timestamp', 'File', 'Threat Level', 'Score', 'Description']);
        foreach ($data['threats'] as $threat) {
            fputcsv($csv, [
                'Threat',
                $threat['timestamp'],
                $threat['file'],
                $threat['threat_level'],
                $threat['threat_score'],
                implode('; ', array_column($threat['threats'], 'description'))
            ]);
        }
        
        // Write file changes
        foreach ($data['file_changes'] as $change) {
            fputcsv($csv, [
                'File Change',
                $change['timestamp'],
                $change['file'],
                $change['severity'],
                '',
                $change['type']
            ]);
        }
        
        fclose($csv);
        return $filename;
    }
    
    private function getHtmlReportTemplate($data) {
        $riskColor = $data['summary']['risk_score'] >= 70 ? 'danger' : 
                    ($data['summary']['risk_score'] >= 40 ? 'warning' : 'success');
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <title>Security Report - {$data['report_id']}</title>
            <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
        </head>
        <body>
            <div class='container mt-4'>
                <h1>Security Report</h1>
                <p><strong>Report ID:</strong> {$data['report_id']}</p>
                <p><strong>Generated:</strong> {$data['generated_at']}</p>
                <p><strong>Period:</strong> {$data['period']['start']} to {$data['period']['end']}</p>
                
                <div class='row mt-4'>
                    <div class='col-md-6'>
                        <div class='card'>
                            <div class='card-header'>
                                <h5>Security Summary</h5>
                            </div>
                            <div class='card-body'>
                                <div class='alert alert-{$riskColor}'>
                                    <h4>Overall Risk Score: {$data['summary']['risk_score']}/100</h4>
                                </div>
                                <p><strong>Total Threats:</strong> {$data['summary']['total_threats']}</p>
                                <p><strong>File Changes:</strong> {$data['summary']['total_file_changes']}</p>
                                <p><strong>Security Events:</strong> {$data['summary']['total_security_events']}</p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-6'>
                        <div class='card'>
                            <div class='card-header'>
                                <h5>Threat Breakdown</h5>
                            </div>
                            <div class='card-body'>
                                <p><span class='badge bg-danger'>Critical: {$data['summary']['threat_levels']['critical']}</span></p>
                                <p><span class='badge bg-warning'>High: {$data['summary']['threat_levels']['high']}</span></p>
                                <p><span class='badge bg-info'>Medium: {$data['summary']['threat_levels']['medium']}</span></p>
                                <p><span class='badge bg-success'>Low: {$data['summary']['threat_levels']['low']}</span></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class='mt-4'>
                    <h3>Recommendations</h3>
                    " . $this->formatRecommendationsHtml($data['recommendations']) . "
                </div>
            </div>
        </body>
        </html>";
    }
    
    private function formatRecommendationsHtml($recommendations) {
        $html = '';
        foreach ($recommendations as $rec) {
            $alertClass = $rec['priority'] === 'critical' ? 'danger' : 
                         ($rec['priority'] === 'high' ? 'warning' : 'info');
            $html .= "
                <div class='alert alert-{$alertClass}'>
                    <h5>{$rec['title']}</h5>
                    <p>{$rec['description']}</p>
                </div>
            ";
        }
        return $html;
    }
    
    private function triggerAlert($alertData) {
        $alertKey = md5($alertData['event'] . ($alertData['details']['file'] ?? ''));
        
        // Check cooldown
        if ($this->isAlertInCooldown($alertKey)) {
            return false;
        }
        
        // Log alert
        $this->logAlert($alertData);
        
        // Send notifications
        if ($this->config['email_alerts'] && !empty($this->config['email_recipients'])) {
            $this->sendEmailAlert($alertData);
        }
        
        if (!empty($this->config['webhook_url'])) {
            $this->sendWebhookAlert($alertData);
        }
        
        // Set cooldown
        $this->setAlertCooldown($alertKey);
        
        return true;
    }
    
    private function isAlertInCooldown($alertKey) {
        $cooldownFile = $this->config['log_directory'] . 'alert_cooldowns.json';
        if (!file_exists($cooldownFile)) {
            return false;
        }
        
        $cooldowns = json_decode(file_get_contents($cooldownFile), true) ?: [];
        $now = time();
        
        return isset($cooldowns[$alertKey]) && 
               ($now - $cooldowns[$alertKey]) < $this->config['alert_cooldown'];
    }
    
    private function setAlertCooldown($alertKey) {
        $cooldownFile = $this->config['log_directory'] . 'alert_cooldowns.json';
        $cooldowns = [];
        
        if (file_exists($cooldownFile)) {
            $cooldowns = json_decode(file_get_contents($cooldownFile), true) ?: [];
        }
        
        $cooldowns[$alertKey] = time();
        file_put_contents($cooldownFile, json_encode($cooldowns));
    }
    
    private function logAlert($alertData) {
        $this->writeLog('alerts', $alertData);
    }
    
    private function sendEmailAlert($alertData) {
        $subject = "Security Alert: " . $alertData['event'];
        $body = "Security Alert Details:\n\n";
        $body .= "Event: " . $alertData['event'] . "\n";
        $body .= "Severity: " . $alertData['severity'] . "\n";
        $body .= "Time: " . date('Y-m-d H:i:s') . "\n\n";
        $body .= "Details: " . json_encode($alertData['details'], JSON_PRETTY_PRINT);
        
        foreach ($this->config['email_recipients'] as $email) {
            mail($email, $subject, $body);
        }
    }
    
    private function sendWebhookAlert($alertData) {
        $payload = json_encode($alertData);
        
        $ch = curl_init($this->config['webhook_url']);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        curl_exec($ch);
        curl_close($ch);
    }
    
    public function cleanupOldLogs() {
        $retentionTime = time() - ($this->config['retention_days'] * 24 * 60 * 60);
        $logDir = $this->config['log_directory'];
        
        $files = glob($logDir . '*.log.*');
        foreach ($files as $file) {
            if (filemtime($file) < $retentionTime) {
                unlink($file);
            }
        }
    }
    
    public function getLogSummary($logType, $hours = 24) {
        $entries = $this->getLogEntriesInPeriod(
            $logType, 
            date('Y-m-d', strtotime("-{$hours} hours")), 
            date('Y-m-d')
        );
        
        return [
            'total_entries' => count($entries),
            'recent_entries' => array_slice($entries, -10),
            'severity_breakdown' => array_count_values(array_column($entries, 'severity'))
        ];
    }
}

?>
