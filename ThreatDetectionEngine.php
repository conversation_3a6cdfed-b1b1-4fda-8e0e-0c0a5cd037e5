<?php

class ThreatDetectionEngine {
    private $threatDatabase = [];
    private $networkPatterns = [];
    private $behaviorPatterns = [];
    private $config = [];
    
    public function __construct($config = []) {
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->initializeThreatDatabase();
        $this->initializeNetworkPatterns();
        $this->initializeBehaviorPatterns();
    }
    
    private function getDefaultConfig() {
        return [
            'enable_network_monitoring' => true,
            'enable_behavior_analysis' => true,
            'enable_real_time_scanning' => true,
            'threat_score_threshold' => 70,
            'auto_quarantine_threshold' => 90,
            'log_file' => 'logs/threat_detection.log',
            'threat_db_file' => 'data/threat_signatures.json'
        ];
    }
    
    private function initializeThreatDatabase() {
        $this->threatDatabase = [
            'web_shells' => [
                'c99' => [
                    'signatures' => ['c99shell', 'Safe_mode', 'File manager', 'eval(gzinflate(base64_decode'],
                    'risk_score' => 95
                ],
                'r57' => [
                    'signatures' => ['r57shell', 'uname -a', 'ServerIP', 'whoami'],
                    'risk_score' => 95
                ],
                'wso' => [
                    'signatures' => ['WSO', 'Web Shell by oRb', 'FilesMan', 'Sec. Info'],
                    'risk_score' => 95
                ],
                'b374k' => [
                    'signatures' => ['b374k', 'Jayalah Indonesiaku', 'IndoXploit'],
                    'risk_score' => 95
                ]
            ],
            'backdoors' => [
                'generic_php_backdoor' => [
                    'signatures' => ['eval($_POST', 'system($_GET', 'exec($_REQUEST', 'shell_exec($_'],
                    'risk_score' => 90
                ],
                'obfuscated_backdoor' => [
                    'signatures' => ['base64_decode', 'gzinflate', 'str_rot13', 'eval(gzuncompress'],
                    'risk_score' => 85
                ]
            ],
            'malware' => [
                'trojan_downloader' => [
                    'signatures' => ['file_get_contents("http', 'curl_exec', 'fsockopen', 'wget'],
                    'risk_score' => 80
                ],
                'data_exfiltration' => [
                    'signatures' => ['mail(', 'ftp_connect', 'mysql_connect', 'file_put_contents'],
                    'risk_score' => 75
                ]
            ],
            'exploits' => [
                'sql_injection' => [
                    'signatures' => ['UNION SELECT', 'DROP TABLE', '1=1--', 'OR 1=1'],
                    'risk_score' => 85
                ],
                'xss_payload' => [
                    'signatures' => ['<script>', 'javascript:', 'onload=', 'onerror='],
                    'risk_score' => 70
                ],
                'lfi_rfi' => [
                    'signatures' => ['../../../', 'php://input', 'data://', 'expect://'],
                    'risk_score' => 80
                ]
            ]
        ];
    }
    
    private function initializeNetworkPatterns() {
        $this->networkPatterns = [
            'suspicious_connections' => [
                'tor_exit_nodes' => '/\.onion/',
                'suspicious_domains' => '/\.(tk|ml|ga|cf)$/',
                'ip_addresses' => '/\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/',
                'base64_urls' => '/[a-zA-Z0-9+\/]{20,}={0,2}/'
            ],
            'attack_patterns' => [
                'port_scanning' => '/nmap|masscan|zmap/',
                'brute_force' => '/hydra|medusa|brutespray/',
                'vulnerability_scanning' => '/nikto|dirb|gobuster|sqlmap/'
            ]
        ];
    }
    
    private function initializeBehaviorPatterns() {
        $this->behaviorPatterns = [
            'file_operations' => [
                'mass_file_creation' => 10, // files per minute
                'mass_file_deletion' => 5,
                'suspicious_file_access' => ['/etc/passwd', '/etc/shadow', 'wp-config.php']
            ],
            'network_behavior' => [
                'excessive_requests' => 100, // requests per minute
                'failed_login_attempts' => 10,
                'unusual_user_agents' => ['sqlmap', 'nikto', 'dirb', 'gobuster']
            ]
        ];
    }
    
    public function analyzeFile($filePath, $content = null) {
        if ($content === null) {
            if (!file_exists($filePath)) {
                return null;
            }
            $content = file_get_contents($filePath);
        }
        
        $threats = [];
        $totalScore = 0;
        
        // Analyze against threat database
        foreach ($this->threatDatabase as $category => $threats_in_category) {
            foreach ($threats_in_category as $threatName => $threatData) {
                $matches = $this->checkThreatSignatures($content, $threatData['signatures']);
                if (!empty($matches)) {
                    $threats[] = [
                        'category' => $category,
                        'name' => $threatName,
                        'matches' => $matches,
                        'risk_score' => $threatData['risk_score'],
                        'description' => $this->getThreatDescription($category, $threatName)
                    ];
                    $totalScore += $threatData['risk_score'];
                }
            }
        }
        
        // Additional analysis
        $encodingThreats = $this->analyzeEncoding($content);
        $threats = array_merge($threats, $encodingThreats);
        
        $obfuscationThreats = $this->analyzeObfuscation($content);
        $threats = array_merge($threats, $obfuscationThreats);
        
        $networkThreats = $this->analyzeNetworkPatterns($content);
        $threats = array_merge($threats, $networkThreats);
        
        // Calculate final threat score
        foreach (array_merge($encodingThreats, $obfuscationThreats, $networkThreats) as $threat) {
            $totalScore += $threat['risk_score'];
        }
        
        $result = [
            'file' => $filePath,
            'threat_score' => min($totalScore, 100), // Cap at 100
            'threat_level' => $this->calculateThreatLevel($totalScore),
            'threats_detected' => $threats,
            'analysis_timestamp' => time(),
            'recommended_action' => $this->getRecommendedAction($totalScore)
        ];
        
        $this->logThreatAnalysis($result);
        
        return $result;
    }
    
    private function checkThreatSignatures($content, $signatures) {
        $matches = [];
        foreach ($signatures as $signature) {
            if (stripos($content, $signature) !== false) {
                $matches[] = $signature;
            }
        }
        return $matches;
    }
    
    private function analyzeEncoding($content) {
        $threats = [];
        
        // Check for base64 encoding
        $base64Pattern = '/[a-zA-Z0-9+\/]{50,}={0,2}/';
        if (preg_match_all($base64Pattern, $content, $matches)) {
            foreach ($matches[0] as $match) {
                $decoded = base64_decode($match, true);
                if ($decoded !== false && $this->containsSuspiciousCode($decoded)) {
                    $threats[] = [
                        'category' => 'encoding',
                        'name' => 'base64_encoded_threat',
                        'matches' => [substr($match, 0, 50) . '...'],
                        'risk_score' => 80,
                        'description' => 'Base64 encoded suspicious content detected'
                    ];
                }
            }
        }
        
        // Check for hex encoding
        $hexPattern = '/\\\\x[0-9a-f]{2}/i';
        if (preg_match_all($hexPattern, $content, $matches) && count($matches[0]) > 10) {
            $threats[] = [
                'category' => 'encoding',
                'name' => 'hex_encoded_content',
                'matches' => array_slice($matches[0], 0, 5),
                'risk_score' => 70,
                'description' => 'Hex encoded content detected'
            ];
        }
        
        return $threats;
    }
    
    private function analyzeObfuscation($content) {
        $threats = [];
        
        // Check for variable function calls
        $varFuncPattern = '/\$[a-zA-Z_][a-zA-Z0-9_]*\s*\(/';
        if (preg_match_all($varFuncPattern, $content, $matches) && count($matches[0]) > 5) {
            $threats[] = [
                'category' => 'obfuscation',
                'name' => 'variable_functions',
                'matches' => array_slice(array_unique($matches[0]), 0, 5),
                'risk_score' => 60,
                'description' => 'Variable function calls detected (possible obfuscation)'
            ];
        }
        
        // Check for string concatenation obfuscation
        $concatPattern = '/["\'][^"\']*["\'][\s]*\.[\s]*["\'][^"\']*["\']/';
        if (preg_match_all($concatPattern, $content, $matches) && count($matches[0]) > 10) {
            $threats[] = [
                'category' => 'obfuscation',
                'name' => 'string_concatenation',
                'matches' => array_slice($matches[0], 0, 3),
                'risk_score' => 50,
                'description' => 'Excessive string concatenation (possible obfuscation)'
            ];
        }
        
        // Check for chr() function usage
        $chrPattern = '/chr\s*\(\s*\d+\s*\)/i';
        if (preg_match_all($chrPattern, $content, $matches) && count($matches[0]) > 5) {
            $threats[] = [
                'category' => 'obfuscation',
                'name' => 'chr_function_abuse',
                'matches' => array_slice($matches[0], 0, 5),
                'risk_score' => 70,
                'description' => 'Multiple chr() function calls (character obfuscation)'
            ];
        }
        
        return $threats;
    }
    
    private function analyzeNetworkPatterns($content) {
        $threats = [];
        
        foreach ($this->networkPatterns as $category => $patterns) {
            foreach ($patterns as $patternName => $pattern) {
                if (preg_match_all($pattern, $content, $matches)) {
                    $threats[] = [
                        'category' => 'network',
                        'name' => $patternName,
                        'matches' => array_slice(array_unique($matches[0]), 0, 5),
                        'risk_score' => $this->getNetworkThreatScore($patternName),
                        'description' => $this->getNetworkThreatDescription($patternName)
                    ];
                }
            }
        }
        
        return $threats;
    }
    
    private function containsSuspiciousCode($code) {
        $suspiciousKeywords = ['eval', 'exec', 'system', 'shell_exec', 'passthru', 'file_get_contents'];
        foreach ($suspiciousKeywords as $keyword) {
            if (stripos($code, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }
    
    private function calculateThreatLevel($score) {
        if ($score >= 90) return 'critical';
        if ($score >= 70) return 'high';
        if ($score >= 50) return 'medium';
        if ($score >= 30) return 'low';
        return 'minimal';
    }
    
    private function getRecommendedAction($score) {
        if ($score >= $this->config['auto_quarantine_threshold']) {
            return 'immediate_quarantine';
        }
        if ($score >= $this->config['threat_score_threshold']) {
            return 'manual_review';
        }
        if ($score >= 50) {
            return 'monitor';
        }
        return 'none';
    }
    
    private function getThreatDescription($category, $threatName) {
        $descriptions = [
            'web_shells' => [
                'c99' => 'C99 web shell - allows remote command execution',
                'r57' => 'R57 web shell - provides file management and system info',
                'wso' => 'WSO web shell - advanced backdoor with multiple features',
                'b374k' => 'B374k web shell - Indonesian-origin backdoor'
            ],
            'backdoors' => [
                'generic_php_backdoor' => 'Generic PHP backdoor allowing remote code execution',
                'obfuscated_backdoor' => 'Obfuscated backdoor using encoding techniques'
            ],
            'malware' => [
                'trojan_downloader' => 'Trojan downloader - downloads additional malware',
                'data_exfiltration' => 'Data exfiltration tool - steals sensitive information'
            ],
            'exploits' => [
                'sql_injection' => 'SQL injection exploit code',
                'xss_payload' => 'Cross-site scripting payload',
                'lfi_rfi' => 'Local/Remote file inclusion exploit'
            ]
        ];
        
        return $descriptions[$category][$threatName] ?? 'Unknown threat type';
    }
    
    private function getNetworkThreatScore($patternName) {
        $scores = [
            'tor_exit_nodes' => 80,
            'suspicious_domains' => 70,
            'ip_addresses' => 40,
            'base64_urls' => 60,
            'port_scanning' => 90,
            'brute_force' => 85,
            'vulnerability_scanning' => 80
        ];
        
        return $scores[$patternName] ?? 50;
    }
    
    private function getNetworkThreatDescription($patternName) {
        $descriptions = [
            'tor_exit_nodes' => 'Tor network usage detected',
            'suspicious_domains' => 'Suspicious domain names found',
            'ip_addresses' => 'Direct IP address connections',
            'base64_urls' => 'Base64 encoded URLs detected',
            'port_scanning' => 'Port scanning tools detected',
            'brute_force' => 'Brute force attack tools found',
            'vulnerability_scanning' => 'Vulnerability scanning tools detected'
        ];
        
        return $descriptions[$patternName] ?? 'Network-related threat';
    }
    
    public function analyzeBehavior($logData) {
        $behaviorThreats = [];
        
        // Analyze file operations
        if (isset($logData['file_operations'])) {
            $fileOps = $logData['file_operations'];
            
            if ($fileOps['files_created_per_minute'] > $this->behaviorPatterns['file_operations']['mass_file_creation']) {
                $behaviorThreats[] = [
                    'type' => 'mass_file_creation',
                    'severity' => 'high',
                    'description' => 'Unusual number of files created rapidly'
                ];
            }
            
            if ($fileOps['files_deleted_per_minute'] > $this->behaviorPatterns['file_operations']['mass_file_deletion']) {
                $behaviorThreats[] = [
                    'type' => 'mass_file_deletion',
                    'severity' => 'high',
                    'description' => 'Unusual number of files deleted rapidly'
                ];
            }
        }
        
        // Analyze network behavior
        if (isset($logData['network_behavior'])) {
            $netBehavior = $logData['network_behavior'];
            
            if ($netBehavior['requests_per_minute'] > $this->behaviorPatterns['network_behavior']['excessive_requests']) {
                $behaviorThreats[] = [
                    'type' => 'excessive_requests',
                    'severity' => 'medium',
                    'description' => 'Excessive number of requests detected'
                ];
            }
            
            if ($netBehavior['failed_logins'] > $this->behaviorPatterns['network_behavior']['failed_login_attempts']) {
                $behaviorThreats[] = [
                    'type' => 'brute_force_attempt',
                    'severity' => 'high',
                    'description' => 'Multiple failed login attempts detected'
                ];
            }
        }
        
        return $behaviorThreats;
    }
    
    private function logThreatAnalysis($result) {
        $logDir = dirname($this->config['log_file']);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'file' => $result['file'],
            'threat_score' => $result['threat_score'],
            'threat_level' => $result['threat_level'],
            'threats_count' => count($result['threats_detected']),
            'action' => $result['recommended_action']
        ];
        
        file_put_contents(
            $this->config['log_file'],
            json_encode($logEntry) . "\n",
            FILE_APPEND | LOCK_EX
        );
    }
    
    public function updateThreatDatabase($newThreats) {
        $this->threatDatabase = array_merge_recursive($this->threatDatabase, $newThreats);
        
        if (isset($this->config['threat_db_file'])) {
            $dbDir = dirname($this->config['threat_db_file']);
            if (!is_dir($dbDir)) {
                mkdir($dbDir, 0755, true);
            }
            
            file_put_contents(
                $this->config['threat_db_file'],
                json_encode($this->threatDatabase, JSON_PRETTY_PRINT)
            );
        }
    }
    
    public function getThreatDatabase() {
        return $this->threatDatabase;
    }
}

?>
