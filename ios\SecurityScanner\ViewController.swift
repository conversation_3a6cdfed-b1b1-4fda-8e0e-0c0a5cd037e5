import UIKit
import Foundation
import LocalAuthentication

class ViewController: UIViewController {
    
    // MARK: - IBOutlets
    @IBOutlet weak var securityStatusCard: UIView!
    @IBOutlet weak var statusLabel: UILabel!
    @IBOutlet weak var threatCountLabel: UILabel!
    @IBOutlet weak var scanButton: UIButton!
    @IBOutlet weak var monitorButton: UIButton!
    @IBOutlet weak var scanProgressView: UIProgressView!
    @IBOutlet weak var threatsTableView: UITableView!
    @IBOutlet weak var emergencyButton: UIButton!
    
    // MARK: - Properties
    private var securityScanner: SecurityScanner!
    private var fileMonitor: FileSystemMonitor!
    private var apiClient: APIClient!
    private var threats: [ThreatItem] = []
    private var isScanning = false
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        initializeComponents()
        requestPermissions()
        authenticateUser()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refreshSecurityStatus()
    }
    
    // MARK: - Setup
    private func setupUI() {
        title = "Security Scanner"
        
        // Setup security status card
        securityStatusCard.layer.cornerRadius = 12
        securityStatusCard.layer.shadowColor = UIColor.black.cgColor
        securityStatusCard.layer.shadowOffset = CGSize(width: 0, height: 2)
        securityStatusCard.layer.shadowRadius = 8
        securityStatusCard.layer.shadowOpacity = 0.1
        
        // Setup buttons
        scanButton.layer.cornerRadius = 8
        monitorButton.layer.cornerRadius = 8
        emergencyButton.layer.cornerRadius = emergencyButton.frame.width / 2
        
        // Setup table view
        threatsTableView.delegate = self
        threatsTableView.dataSource = self
        threatsTableView.register(ThreatTableViewCell.self, forCellReuseIdentifier: "ThreatCell")
        
        // Setup progress view
        scanProgressView.isHidden = true
        
        updateSecurityStatusUI(status: .secure, threatCount: 0)
    }
    
    private func initializeComponents() {
        securityScanner = SecurityScanner()
        fileMonitor = FileSystemMonitor()
        apiClient = APIClient()
        
        // Set delegates
        securityScanner.delegate = self
        fileMonitor.delegate = self
        apiClient.delegate = self
        
        // Register device
        apiClient.registerDevice()
    }
    
    private func requestPermissions() {
        // Request necessary permissions for iOS
        let authContext = LAContext()
        var error: NSError?
        
        if authContext.canEvaluatePolicy(.biometricAny, error: &error) {
            // Biometric authentication available
        }
        
        // Request notification permissions
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if granted {
                print("Notification permissions granted")
            }
        }
    }
    
    private func authenticateUser() {
        let context = LAContext()
        let reason = "Authenticate to access security scanner"
        
        context.evaluatePolicy(.biometricAny, localizedReason: reason) { [weak self] success, error in
            DispatchQueue.main.async {
                if success {
                    self?.enableSecurityFeatures()
                } else {
                    self?.showAuthenticationError(error)
                }
            }
        }
    }
    
    private func enableSecurityFeatures() {
        scanButton.isEnabled = true
        monitorButton.isEnabled = true
        emergencyButton.isEnabled = true
    }
    
    private func showAuthenticationError(_ error: Error?) {
        let alert = UIAlertController(
            title: "Authentication Required",
            message: "Please authenticate to use security features",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Retry", style: .default) { _ in
            self.authenticateUser()
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        present(alert, animated: true)
    }
    
    // MARK: - Actions
    @IBAction func scanButtonTapped(_ sender: UIButton) {
        startSecurityScan()
    }
    
    @IBAction func monitorButtonTapped(_ sender: UIButton) {
        startFileMonitoring()
    }
    
    @IBAction func emergencyButtonTapped(_ sender: UIButton) {
        performEmergencyScan()
    }
    
    // MARK: - Security Operations
    private func startSecurityScan() {
        guard !isScanning else {
            showAlert(title: "Scan in Progress", message: "A security scan is already running")
            return
        }
        
        isScanning = true
        updateScanningUI(scanning: true)
        
        securityScanner.startScan { [weak self] result in
            DispatchQueue.main.async {
                self?.handleScanResult(result)
            }
        }
    }
    
    private func startFileMonitoring() {
        fileMonitor.startMonitoring()
        
        let alert = UIAlertController(
            title: "File Monitoring Started",
            message: "The app will monitor for suspicious file changes",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
    
    private func performEmergencyScan() {
        let alert = UIAlertController(
            title: "Emergency Security Scan",
            message: "This will perform a comprehensive security scan. Continue?",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Yes", style: .destructive) { _ in
            self.startSecurityScan()
            self.startFileMonitoring()
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        present(alert, animated: true)
    }
    
    private func handleScanResult(_ result: ScanResult) {
        isScanning = false
        updateScanningUI(scanning: false)
        
        switch result {
        case .success(let threats):
            self.threats = threats
            threatsTableView.reloadData()
            updateSecurityStatusUI(status: getSecurityStatus(from: threats), threatCount: threats.count)
            
            // Upload results to API
            apiClient.uploadScanResults(threats)
            
            showAlert(title: "Scan Complete", message: "Found \(threats.count) potential threats")
            
        case .failure(let error):
            showAlert(title: "Scan Failed", message: error.localizedDescription)
        }
    }
    
    private func getSecurityStatus(from threats: [ThreatItem]) -> SecurityStatus {
        let criticalCount = threats.filter { $0.severity == .critical }.count
        let highCount = threats.filter { $0.severity == .high }.count
        
        if criticalCount > 0 {
            return .critical
        } else if highCount > 0 {
            return .high
        } else if threats.count > 0 {
            return .medium
        } else {
            return .secure
        }
    }
    
    // MARK: - UI Updates
    private func updateScanningUI(scanning: Bool) {
        scanButton.isEnabled = !scanning
        scanProgressView.isHidden = !scanning
        
        if scanning {
            statusLabel.text = "Scanning for threats..."
            scanProgressView.progress = 0.0
        }
    }
    
    private func updateSecurityStatusUI(status: SecurityStatus, threatCount: Int) {
        threatCountLabel.text = "\(threatCount)"
        
        switch status {
        case .secure:
            statusLabel.text = "SYSTEM SECURE"
            statusLabel.textColor = .systemGreen
            securityStatusCard.backgroundColor = UIColor.systemGreen.withAlphaComponent(0.1)
            
        case .medium:
            statusLabel.text = "MINOR THREATS DETECTED"
            statusLabel.textColor = .systemOrange
            securityStatusCard.backgroundColor = UIColor.systemOrange.withAlphaComponent(0.1)
            
        case .high:
            statusLabel.text = "HIGH RISK THREATS FOUND"
            statusLabel.textColor = .systemRed
            securityStatusCard.backgroundColor = UIColor.systemRed.withAlphaComponent(0.1)
            
        case .critical:
            statusLabel.text = "CRITICAL THREATS DETECTED"
            statusLabel.textColor = .systemRed
            securityStatusCard.backgroundColor = UIColor.systemRed.withAlphaComponent(0.2)
            
            // Trigger haptic feedback for critical threats
            let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
            impactFeedback.impactOccurred()
        }
    }
    
    private func refreshSecurityStatus() {
        apiClient.getScanStatus { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let status):
                    self?.updateUIWithServerStatus(status)
                case .failure(let error):
                    print("Failed to get scan status: \(error)")
                }
            }
        }
    }
    
    private func updateUIWithServerStatus(_ status: ServerScanStatus) {
        threatCountLabel.text = "\(status.totalThreats)"
        // Update other UI elements based on server status
    }
    
    // MARK: - Helper Methods
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
    
    private func showThreatDetail(_ threat: ThreatItem) {
        let storyboard = UIStoryboard(name: "Main", bundle: nil)
        if let detailVC = storyboard.instantiateViewController(withIdentifier: "ThreatDetailViewController") as? ThreatDetailViewController {
            detailVC.threat = threat
            navigationController?.pushViewController(detailVC, animated: true)
        }
    }
}

// MARK: - TableView DataSource & Delegate
extension ViewController: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return threats.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "ThreatCell", for: indexPath) as! ThreatTableViewCell
        cell.configure(with: threats[indexPath.row])
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        showThreatDetail(threats[indexPath.row])
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 80
    }
}

// MARK: - SecurityScanner Delegate
extension ViewController: SecurityScannerDelegate {
    
    func scanDidStart() {
        DispatchQueue.main.async {
            self.updateScanningUI(scanning: true)
        }
    }
    
    func scanDidUpdateProgress(_ progress: Float, currentFile: String) {
        DispatchQueue.main.async {
            self.scanProgressView.progress = progress
            self.statusLabel.text = "Scanning: \(currentFile)"
        }
    }
    
    func scanDidComplete(with threats: [ThreatItem]) {
        DispatchQueue.main.async {
            self.handleScanResult(.success(threats))
        }
    }
    
    func scanDidFail(with error: Error) {
        DispatchQueue.main.async {
            self.handleScanResult(.failure(error))
        }
    }
}

// MARK: - FileSystemMonitor Delegate
extension ViewController: FileSystemMonitorDelegate {
    
    func fileDidChange(at path: String, changeType: FileChangeType) {
        DispatchQueue.main.async {
            self.showFileChangeAlert(path: path, changeType: changeType)
        }
    }
    
    func suspiciousActivityDetected(description: String, severity: Int) {
        DispatchQueue.main.async {
            self.showSuspiciousActivityAlert(description: description, severity: severity)
        }
    }
    
    private func showFileChangeAlert(path: String, changeType: FileChangeType) {
        let alert = UIAlertController(
            title: "File System Change Detected",
            message: "File: \(path)\nChange: \(changeType.description)",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Scan File", style: .default) { _ in
            self.securityScanner.scanSpecificFile(at: path)
        })
        
        alert.addAction(UIAlertAction(title: "Ignore", style: .cancel))
        
        present(alert, animated: true)
    }
    
    private func showSuspiciousActivityAlert(description: String, severity: Int) {
        let title = severity >= 8 ? "CRITICAL SECURITY ALERT" : "Security Alert"
        
        let alert = UIAlertController(
            title: title,
            message: description,
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Full Scan", style: .destructive) { _ in
            self.startSecurityScan()
        })
        
        alert.addAction(UIAlertAction(title: "Dismiss", style: .cancel))
        
        present(alert, animated: true)
    }
}

// MARK: - APIClient Delegate
extension ViewController: APIClientDelegate {
    
    func apiClientDidConnect() {
        print("Connected to security API")
    }
    
    func apiClientDidDisconnect(with error: Error?) {
        print("Disconnected from security API: \(error?.localizedDescription ?? "Unknown error")")
    }
    
    func apiClientDidReceiveUpdate(_ update: SecurityUpdate) {
        DispatchQueue.main.async {
            // Handle real-time security updates
            self.refreshSecurityStatus()
        }
    }
}

// MARK: - Enums
enum SecurityStatus {
    case secure
    case medium
    case high
    case critical
}

enum ScanResult {
    case success([ThreatItem])
    case failure(Error)
}
